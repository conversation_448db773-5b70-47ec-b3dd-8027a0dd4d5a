<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_xmspf.xmspf_20001REQ" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_xmspf.xmspf_20001REQ">
    <xs:complexType name="serviceno">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="服务编号" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="usr">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="用户名" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="pwd">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="用户密码" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="optname">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="实际操作人" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="signmsg">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="签名信息" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="head">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="serviceno" nillable="true" type="serviceno"/>
            <xs:element name="usr" nillable="true" type="usr"/>
            <xs:element name="pwd" nillable="true" type="pwd"/>
            <xs:element name="optname" nillable="true" type="optname"/>
            <xs:element name="signmsg" nillable="true" type="signmsg"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="accountname">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="账户名称" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="accountno">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="账号" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="bankid">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="开户银行代码" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="accountname" nillable="true" type="accountname"/>
            <xs:element name="accountno" nillable="true" type="accountno"/>
            <xs:element name="bankid" nillable="true" type="bankid"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="content">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="head"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="xmspf_20001REQ">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="content" nillable="true" type="content"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
