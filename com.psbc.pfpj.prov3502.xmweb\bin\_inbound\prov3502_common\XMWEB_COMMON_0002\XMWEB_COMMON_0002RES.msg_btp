<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="PSBC-CD173" category="message" create-date="2021-08-20 15:53:30" version="*******">
  <message-definition bitmap-format="Binary" bitmap-length="0" bitmap-type="unionpay" category="json" filter-null="false" id="XMWEB_COMMON_0002RES" name="响应报文" namespace="prov3502_common.XMWEB_COMMON_0002RES" orderly="false" xml-pack-simple-node="false" xml-pack-statement="true" nodef-json="false">
    <message-ref display-name="head" name="head" ref="common_head_res" ref-path="" seqno="0">
      <description></description>
    </message-ref>
    <group-message-item display-name="body" name="body" seqno="1" xml-prefix="">
      <repeated-item display-name="list" name="list" out-key="true" repeat-field="" repeat-times="-1" seqno="0">
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR28" display-name="委托单位代码-STR28" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="merch_id" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR29" display-name="业务代码-STR29" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="ope_cd" pad-char="0x20" seqno="1" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR30" display-name="子业务代码-STR30" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="corp_cd" pad-char="0x20" seqno="2" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR31" display-name="用户号-STR31" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="pay_id" pad-char="0x20" seqno="3" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR32" display-name="卡号/账号-STR32" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="acc_id" pad-char="0x20" seqno="4" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR33" display-name="签约日期-STR33" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="commi_dt" pad-char="0x20" seqno="5" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR34" display-name="签约生效日期-STR34" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="effect_dt" pad-char="0x20" seqno="6" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR35" display-name="解约日期-STR35" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="pre_commi_dt" pad-char="0x20" seqno="7" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR36" display-name="解约生效日期-STR36" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="pre_effect_dt" pad-char="0x20" seqno="8" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR37" display-name="签约状态状态-STR37" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="stat_cd" pad-char="0x20" seqno="9" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" dict-id="STR38" display-name="卡号/账号-STR38" field-type="normal" is-required="false" name="acc_card_id" pad-char="0x20" seqno="10" validate-rule="" value-mode="normal" xml-field-type="VALUE" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
      </repeated-item>
      <ext-property/>
    </group-message-item>
  </message-definition>
</configuration>