<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="com.psbc.pfpj.prov3502.xmssdsf.xmssdsf">
    <!-- 查询当日该用户号是否签约 -->
    <select id="query_commi_info" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select 
			merch_id,
			ope_cd,
			pay_id,
			payer_id,
			acc_id,
			acc_card_id,
			card_pk_fg,
			commi_dt,
			time_stamp,
			stat_cd
		from 
			tb_pay_commi_info
		where
			merch_id = #merch_id#
			and ope_cd = #ope_cd#
			and acc_id = #acc_id#</select>
    <!-- 修改签约表 -->
    <update id="update_commi_info" parameterClass="java.util.HashMap">update 
    		tb_pay_commi_info
    	set
    		stat_cd = #stat_cd#
    	where
			merch_id = #merch_id#
			and ope_cd = #ope_cd#
			and acc_id = #acc_id#</update>
    <!-- 插入签约表 -->
    <insert id="add_commi_info" parameterClass="java.util.HashMap">insert into tb_pay_commi_info(
			merch_id,
			ope_cd,
			pay_id,
			payer_id,
			acc_id,
			acc_card_id,
			card_pk_fg,
			commi_dt,
			time_stamp,
			stat_cd
    	)values(
    		#merch_id#,
			#ope_cd#,
			#pay_id#,
			#payer_id#,
			#acc_id#,
			#acc_card_id#,
			#card_pk_fg#,
			#commi_dt#,
			#time_stamp#,
			#stat_cd#
    	)</insert>
    <!-- 查询用户是否签约 -->
    <select id="qry_commi_info" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select 
			stat_cd
		from 
			tb_pay_commi_info
		where
			merch_id = #merch_id#
			and ope_cd = #ope_cd#
			and pay_id = #pay_id#
			and sub_key = #merch_id#</select>
    <!-- 查询子业务-委托项目代号参数 -->
    <select id="qry_merch_ope_sysparam" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select 
			param_val
		from 
			tb_merch_ope_sysparam
		where
			merch_id = #merch_id#
			and ope_cd = #ope_cd#
			and param_cd = #param_cd#</select>
    <!-- 子业务代码转换 -->
    <select id="qry_tb_merch_sub_ope_memo" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select 
			memo4
		from 
			tb_merch_sub_ope
		where
			merch_id = #merch_id#
			and ope_cd = #ope_cd#
			and merch_inst_id = #merch_inst_id#</select>
    <!-- 查询委托单位业务参数表 tb_merch_ope-->
    <select id="query_tb_merch_ope" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select
			merch_ope_type
		from 
			tb_merch_ope
		where
			merch_id = #merch_id#
			and ope_cd = #ope_cd#</select>
    <!-- 查询流水表缴费明细 tb_int_txn_log 1-联机-->
    <select id="query_txn_log" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        <!--select
			case when grouping(a.tran_dt)=0 and grouping(a.merch_id)=1 then '总计' else a.tran_dt end setdate,
			case when grouping(a.ope_cd)=1 and grouping(a.merch_id)=0 then '小计' else a.ope_cd end opecd1,
			case when grouping(a.merch_id)=0 and grouping(a.ope_cd)=1 then null else a.merch_id end merchid1,
			substring(a.pay_id,0,7) as str30,
			b.sub_ope_nm as str31,
			count(a.tran_at) num,
			sum(a.tran_at) amt
		from
			tb_int_txn_log_temp a,
			tb_merch_sub_ope b
		where 
			a.ope_cd = b.ope_cd
		and a.merch_id = b.merch_id
		and merch_id = #merch_id#
		and ope_cd = #ope_cd#
		and substring(a.pay_id,0,7) = b.sub_ope_id
		group by grouping sets
			(
			(a.tran_dt,
			a.ope_cd,
			b.sub_ope_nm,
			substring(a.pay_id,0,7),
			a.merch_id)
			,rollup(a.tran_dt,a.merch_id)
			)
		having a.tran_dt = #tran_dt#-->
        <!--select
			a.tran_dt as setdate,
			case when grouping(concat(a.ope_cd,'_',a.merch_id))=0 and grouping(substring(a.pay_id,0,7))=1 then '小计' else concat(a.ope_cd,'_',a.merch_id) end str,
			substring(a.pay_id,0,7) as str30,
			b.sub_ope_nm as str31,
			count(a.tran_at) num,
			sum(a.tran_at) amt
		from 
			tb_int_txn_log_temp a,
			tb_merch_sub_ope b
		where 
			a.ope_cd = b.ope_cd
		and a.merch_id = b.merch_id
		and a.merch_id in ('350200050235','350200050238')
		and substring(a.pay_id,0,7) = b.sub_ope_id
		group by grouping sets
					(
						(a.tran_dt,
						concat(a.ope_cd,'_',a.merch_id),
						substring(a.pay_id,0,7),
						b.sub_ope_nm
						),
						rollup(a.tran_dt,concat(a.ope_cd,'_',a.merch_id))
					)
		having a.tran_dt = #tran_dt#--><![CDATA[select
			q.local_dt as setdate,
			case
				when grouping(concat(q.ope_cd, '_', q.merch_id))= 0
				and grouping(q.pay_id)= 1 then '小计'
				else concat(q.ope_cd, '_', q.merch_id)
			end str,
			q.pay_id as str30,
			q.str31,
			count(q.tran_at) num,
			sum(q.tran_at) amt
		from
			(
			select
				a.local_dt,
				a.ope_cd,
				a.merch_id,
				substring(a.pay_id, 0, 7) pay_id,
				b.sub_ope_nm as str31,
				a.tran_at,
				a.tran_id
			from
				tb_int_txn_log_temp a,
				tb_merch_sub_ope b
			where
				a.ope_cd = b.ope_cd
				and a.merch_id = b.merch_id
				and a.merch_id in ('350200050235','350200050238')
				and a.tran_stat_cd = '011'
				and a.ope_cd = '198020'
				and substring(a.pay_id, 0, 7) = b.sub_ope_id
				and a.local_dt between #bng_date# and #end_date#
		union
			select
				a.local_dt,
				a.ope_cd,
				a.merch_id,
				'' as pay_id,
				b.prdt_nm as str31,
				a.tran_at,
				a.tran_id
			from
				tb_int_txn_log_temp a,
				tb_merch_ope b
			where
				a.ope_cd = b.ope_cd
				and a.merch_id = b.merch_id
				and a.merch_id in ('350200050235')
				and a.tran_stat_cd = '011'
				and a.ope_cd <> '198020'
				and a.unite_clr_dt between #bng_date# and #end_date# ) q
		group by
			grouping sets ( (q.local_dt,
			concat(q.ope_cd, '_', q.merch_id),
			q.pay_id,
			q.str31 ),
			rollup(q.local_dt,
			concat(q.ope_cd, '_', q.merch_id)) )
		order by q.local_dt
			
			]]></select>
    <!-- 查询批量明细表 tb_batch_tran_dtl 2-批量-->
    <select id="query_tb_batch_tran_dtl" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
        <!--select 
			case when grouping(a.tran_dt)=0 and grouping(a.merch_id)=1 then '总计' else a.tran_dt end setdate,
			case when grouping(a.ope_cd)=1 and grouping(a.merch_id)=0 then '小计' else a.ope_cd end opecd1,
			case when grouping(a.merch_id)=0 and grouping(a.ope_cd)=1 then null else a.merch_id end merchid1,
			a.corp_cd as str30,
			b.sub_ope_nm as str31,
			count(a.tran_at) num,
			sum(a.tran_at) amt
		from 
			tb_batch_tran_dtl a,
			tb_merch_sub_ope b
		where 
			a.corp_cd = b.sub_ope_id
		and a.ope_cd = b.ope_cd
		and a.merch_id = b.merch_id
		and a.merch_id = #merch_id#
		and a.ope_cd = #ope_cd#
		group by grouping sets
			(
			(a.tran_dt,
			a.ope_cd,
			a.merch_id,
			a.corp_cd,
			b.sub_ope_nm)
			,rollup(a.tran_dt,a.merch_id)
			)
		having a.tran_dt = #tran_dt#-->
		select 
			a.tran_dt as setdate,
			case when grouping(concat(a.ope_cd,'_',a.merch_id))=0 and grouping(a.corp_cd)=1 then '小计' else concat(a.ope_cd,'_',a.merch_id) end str,
			a.corp_cd as str30,
			b.sub_ope_nm as str31,
			count(a.tran_at) num,
			sum(a.tran_at) amt
		from 
			tb_batch_tran_dtl a,
			tb_merch_sub_ope b
		where 
			a.corp_cd = b.sub_ope_id
		and a.ope_cd = b.ope_cd
		and a.merch_id = b.merch_id
		and a.merch_id in ('350200050236','350200050237')
		group by grouping sets(
			(a.tran_dt,
			concat(a.ope_cd,'_',a.merch_id),
			a.corp_cd,
			b.sub_ope_nm),
			rollup(a.tran_dt,concat(a.ope_cd,'_',a.merch_id))
		)
		having a.tran_dt between #bng_date# and #end_date#
		order by a.tran_dt
	</select>
    <!-- 查询厦门银联公共支付对账结果表 tb_3502_xmykt_dz_result-->
    <select id="query_dz_result" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			a.tran_dt,
			a.merch_id,
			a.ope_cd,
			a.type_cd,
			a.user_id,
			a.record_sta,
			a.tran_cd,
			a.tran_nm,
			a.merch_dt,
			a.host_dt,
			a.tran_sq,
			a.acc_card_id,
			a.tran_amt,
			a.oth_msg1,
			b.tran_stat_cd
		from 
			tb_3502_xmykt_dz_result a
		left join
			tb_int_txn_log_temp b
		on 
			a.tran_sq = b.tran_sq
		where 
			a.record_type = #record_type#
			and a.merch_dt = #merch_dt#
			<isNotEmpty property="record_sta">and a.record_sta = #record_sta#</isNotEmpty>
        <isNotEmpty property="merch_id">and a.merch_id = #merch_id#</isNotEmpty>
        <isNotEmpty property="ope_cd">and a.ope_cd = #ope_cd#</isNotEmpty>
        <isNotEmpty property="user_id">and a.user_id = #user_id#</isNotEmpty>
        <isNotEmpty property="acc_card_id">and a.acc_card_id = #acc_card_id#</isNotEmpty>
        <isNotEmpty property="oth_msg1">and a.oth_msg1 = #oth_msg1#</isNotEmpty>
    </select>
    <!-- 插入密钥表 -->
    <insert id="add_tb_keys" parameterClass="java.util.HashMap">insert into tb_keys(
			unit_code,
			unit_type,
			key,
			creat_flag,
			time_stamp,
			sysenc_flag,
			cur_mmk_index,
			cur_pin_index,
			cur_mac_index
    	)values(
    		#merch_id#,
			'98',
			'0',
			#payer_id#,
			'1',
			'0',
			'0',
			'0'
    	)</insert>
    <!-- 插入密钥表  客管传入的新增 -->
    <insert id="add_tb_keys2" parameterClass="java.util.HashMap">insert into tb_keys(
			unit_code,
			unit_type,
			key,
			creat_flag,
			time_stamp,
			sysenc_flag,
			cur_mmk_index,
			cur_pin_index,
			cur_mac_index
    	)values(
    		#merch_id#,
			'98',
			#key#,
			'0',
			#time_stamp#,
			'1',
			'0',
			'0',
			'0'
    	)</insert>
    <!-- 查询密钥表 -->
    <select id="qry_tb_keys" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select 
			unit_code,key
		from 
			tb_keys
		where
			unit_code = #merch_id#</select>
    <!-- 修改密钥表  客管传入的-->
    <update id="upt_tb_keys" parameterClass="java.util.HashMap">update 
    		tb_keys
    	set
    		key = #key#,
    		time_stamp = #time_stamp#
    	where
			unit_code = #merch_id#</update>
</sqlMap>