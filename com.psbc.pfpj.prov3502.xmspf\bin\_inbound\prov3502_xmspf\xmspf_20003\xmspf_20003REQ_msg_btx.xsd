<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_xmspf.xmspf_20003REQ" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_xmspf.xmspf_20003REQ">
    <xs:complexType name="serviceno">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="服务编号" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="usr">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="用户名" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="pwd">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="用户密码" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="optname">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="实际操作人" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="signmsg">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="签名信息" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="bodySign">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="签名信息" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="head">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="serviceno" nillable="true" type="serviceno"/>
            <xs:element name="usr" nillable="true" type="usr"/>
            <xs:element name="pwd" nillable="true" type="pwd"/>
            <xs:element name="optname" nillable="true" type="optname"/>
            <xs:element name="signmsg" nillable="true" type="signmsg"/>
            <xs:element name="bodySign" nillable="true" type="bodySign"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="body" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="content">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="head"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="xmspf_20003REQ">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="content" nillable="true" type="content"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
