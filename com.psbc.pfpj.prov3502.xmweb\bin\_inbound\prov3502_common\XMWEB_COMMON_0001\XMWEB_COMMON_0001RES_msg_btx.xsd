<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_common.XMWEB_COMMON_0001RES" xmlns:msg_head_r="prov3502_8583.msg_head_r" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_common.XMWEB_COMMON_0001RES">
    <xs:import namespace="prov3502_8583.msg_head_r" schemaLocation="../prov3502_8583/msg_head_r_msg_btx.xsd"/>
    <xs:complexType name="XMWEB_COMMON_0001RES">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="报文体" nillable="true" type="msg_head_r:msg_head_r"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
