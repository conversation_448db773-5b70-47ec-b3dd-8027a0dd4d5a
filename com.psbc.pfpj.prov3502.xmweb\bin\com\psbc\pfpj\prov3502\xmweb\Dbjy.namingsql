<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="com.psbc.pfpj.prov3502.xmweb.Dbjy">
    <!-- 发起单边解约 解约类型 1-向委托方发起-->
    <insert id="send_dbjy_1" parameterClass="java.util.HashMap">INSERT INTO tb_3502_xmykt_record(
            tran_dt,
			sub_key,
			agent_key,
			record_id,
			item_id,
			trace_id,
			revole_dt,
			acc_card_id,
			invalid_dt,
			unite_id,
			merch_id,
			ope_cd,
			type_cd,
			user_id,
			revoke_case,
			fee_amt,
			fee_fint,
			fee_hand,
			fee_service,
			batch_id,
			batch_seq_no,
			paper_id,
			cust_name,
			cust_addr,
			cust_tel,
			post_code,
			host_rsp,
			fail_tx,
			misc_tx
		)
      	VALUES(
			#tran_dt#,
			#sub_key#,
			#agent_key#,
			#record_id#,
			#item_id#,
			#trace_id#,
			#revole_dt#,
			#acc_card_id#,
			#invalid_dt#,
			#unite_id#,
			#merch_id#,
			#ope_cd#,
			#type_cd#,
			#user_id#,
			#revoke_case#,
			#fee_amt#,
			#fee_fint#,
			#fee_hand#,
			#fee_service#,
			#batch_id#,
			#batch_seq_no#,
			#paper_id#,
			#cust_name#,
			#cust_addr#,
			#cust_tel#,
			#post_code#,
			#host_rsp#,
			#fail_tx#,
			#misc_tx#
       	)</insert>
    <!-- 发起单边解约 解约类型 3-向外联 1查询-->
    <select id="send_dbjy_31" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">SELECT
			merch_id,
			ope_cd,
			pay_id,
			sub_key,
			corp_cd,
			payer_id,
			stat_cd,
			acc_id,
			acc_card_id,
			acc_name,
			open_inst_id,
			acc_bank_flag,
			card_pk_fg,
			sign_name,
			phone_id,
			paper_type_cd,
			paper_id,
			cust_addr,
			zip_code,
			home_addr,
			prepay_at,
			commi_dt,
			effect_dt,
			pre_commi_dt,
			pre_effect_dt,
			pay_key_msg,
			pay_limit_num,
			min_pay_at,
			max_pay_at,
			due_term,
			last_pay_dt,
			pay_tol_num,
			update_dt,
			update_inst_id,
			update_tlr_id,
			time_stamp,
			mark1,
			mark2,
			mark3
		FROM
			tb_pay_commi_info
		where
			ope_cd = #ope_cd#
			and merch_id = #merch_id#
			and pay_id = #pay_id#</select>
    <!-- 发起单边解约 解约类型 3-向外联 2修改-->
    <update id="send_dbjy_32" parameterClass="java.util.HashMap">UPDATE 
    		tb_pay_commi_info
    	SET
    		stat_cd = '2'
		WHERE
			ope_cd = #ope_cd#
			and merch_id = #merch_id#
			and pay_id = #pay_id#</update>
    <!-- 发起单边解约 解约类型 3-向外联 3添加-->
    <insert id="send_dbjy_33" parameterClass="java.util.HashMap">INSERT INTO tb_pay_commi_info_his(
            tran_dt,
			tran_tm,
			tran_code,
			tran_memo,
			tran_inst_id,
			tran_inst_type,
			tran_tlr_id,
			merch_id,
			ope_cd,
			pay_id,
			sub_key,
			corp_cd,
			payer_id,
			stat_cd,
			acc_id,
			acc_card_id,
			acc_name,
			open_inst_id,
			acc_bank_flag,
			card_pk_fg,
			sign_name,
			phone_id,
			paper_type_cd,
			paper_id,
			cust_addr,
			zip_code,
			home_addr,
			prepay_at,
			commi_dt,
			effect_dt,
			pre_commi_dt,
			pre_effect_dt,
			pay_key_msg,
			pay_limit_num,
			min_pay_at,
			max_pay_at,
			due_term,
			mark1,
			mark2,
			mark3,
			time_stamp
		)
      	VALUES(
			#tran_dt#,
			#tran_tm#,
			#tran_code#,
			#tran_memo#,
			#tran_inst_id#,
			#tran_inst_type#,
			#tran_tlr_id#,
			#merch_id#,
			#ope_cd#,
			#pay_id#,
			#sub_key#,
			#corp_cd#,
			#payer_id#,
			#stat_cd#,
			#acc_id#,
			#acc_card_id#,
			#acc_name#,
			#open_inst_id#,
			#acc_bank_flag#,
			#card_pk_fg#,
			#sign_name#,
			#phone_id#,
			#paper_type_cd#,
			#paper_id#,
			#cust_addr#,
			#zip_code#,
			#home_addr#,
			#prepay_at#,
			#commi_dt#,
			#effect_dt#,
			#pre_commi_dt#,
			#pre_effect_dt#,
			#pay_key_msg#,
			#pay_limit_num#,
			#min_pay_at#,
			#max_pay_at#,
			#due_term#,
			#mark1#,
			#mark2#,
			#mark3#,
			#time_stamp#
       	)</insert>
    <!--以下为签约功能-->
    <!-- 插入签约表 -->
    <insert id="add_commi_info" parameterClass="java.util.HashMap">insert into tb_pay_commi_info(
			merch_id,
			ope_cd,
			pay_id,
			sub_key,
			corp_cd,
			payer_id,
			stat_cd,
			acc_id,
			acc_card_id,
			acc_name,
			card_pk_fg,
			sign_name,
			phone_id,
			paper_type_cd,
			commi_dt,
			cust_addr,
			home_addr,
			time_stamp
    	)values(			
			#merch_id#,
			#ope_cd#,
			#pay_id#,
			#sub_key#,
			#corp_cd#,
			#payer_id#,
			#stat_cd#,
			#acc_id#,
			#acc_card_id#,
			#acc_name#,
			#card_pk_fg#,
			#sign_name#,
			#phone_id#,
			#paper_type_cd#,
			#commi_dt#,
			#cust_addr#,
			#home_addr#,
			to_timestamp(#time_stamp#,'yyyymmddhh24miss')
    	)</insert>
    <!-- -->
    <update id="update_commi_info" parameterClass="java.util.HashMap">UPDATE 
    		tb_pay_commi_info
    	SET
    		sub_key = #sub_key#,
			corp_cd = #corp_cd#,
			payer_id = #payer_id#,
			stat_cd = #stat_cd#,
			acc_id = #acc_id#,
			acc_card_id = #acc_card_id#, 
			acc_name = #acc_name#,
			card_pk_fg = #card_pk_fg#,
			sign_name = #sign_name#,
			phone_id = #paper_type_cd#,
			paper_type_cd = #paper_type_cd#,
			commi_dt = #commi_dt#,
			cust_addr = #cust_addr#,
			home_addr = #home_addr#,
			time_stamp = to_timestamp(#time_stamp#,'yyyymmddhh24miss')
		WHERE
			ope_cd = #ope_cd#
			and merch_id = #merch_id#
			and pay_id = #pay_id#</update>
</sqlMap>