<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_com.XMWEB_COM_MerchDelRES" xmlns:common_head_res="prov3502_common.common_head_res" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_com.XMWEB_COM_MerchDelRES">
    <xs:import namespace="prov3502_common.common_head_res" schemaLocation="../prov3502_common/common_head_res_msg_btx.xsd"/>
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="XMWEB_COM_MerchDelRES">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="common_head_res:common_head_res"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
