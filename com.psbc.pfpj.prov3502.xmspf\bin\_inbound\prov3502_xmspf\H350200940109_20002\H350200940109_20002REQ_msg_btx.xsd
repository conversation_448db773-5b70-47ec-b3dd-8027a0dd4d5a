<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_xmspf.H350200940109_20002REQ" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xmspf_ds_head="com.xmspf_ds_head" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_xmspf.H350200940109_20002REQ">
    <xs:import namespace="com.xmspf_ds_head" schemaLocation="../com/xmspf_ds_head_msg_btx.xsd"/>
    <xs:complexType name="H350200940109_20002REQ">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="ref001" nillable="true" type="xmspf_ds_head:xmspf_ds_head"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
