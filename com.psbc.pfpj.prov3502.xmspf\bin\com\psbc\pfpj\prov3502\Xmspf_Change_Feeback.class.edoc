<map>
  <entry>
    <string>com.psbc.pfpj.prov3502.Xmspf_Change_Feeback.CommonHost(com.primeton.btp.api.engine.context.IKernelServiceContext)</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;void com.psbc.pfpj.prov3502.Xmspf_Change_Feeback.CommonHost(IKernelServiceContext context)&lt;/b&gt;&lt;br/&gt;&lt;p&gt; 通用调用后台，覆盖返回值&#x0D;
 &lt;dl&gt;&lt;dt&gt;参数：&lt;/dt&gt;&lt;dd&gt;&lt;b&gt;context&lt;/b&gt;&lt;/dd&gt;&lt;dt&gt;返回：&lt;/dt&gt;&lt;dd&gt;&lt;/dd&gt;&lt;/dl&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
  <entry>
    <string>com.psbc.pfpj.prov3502.Xmspf_Change_Feeback.Change_Feeback(com.primeton.btp.api.engine.context.IKernelServiceContext)</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;void com.psbc.pfpj.prov3502.Xmspf_Change_Feeback.Change_Feeback(IKernelServiceContext context)&lt;/b&gt;&lt;br/&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
  <entry>
    <string>Xmspf_Change_Feeback</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;com.psbc.pfpj.prov3502.Xmspf_Change_Feeback&lt;/b&gt;&lt;br/&gt;&lt;p&gt; &lt;dl&gt;&lt;dt&gt;作者:&lt;/dt&gt;&lt;dd&gt;PSBC-CD173&lt;/dd&gt;&lt;dt&gt;@date&lt;/dt&gt;&lt;dd&gt;2021-09-08 08:55:14&lt;/dd&gt;&lt;/dl&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
  <entry>
    <string>com.psbc.pfpj.prov3502.Xmspf_Change_Feeback.HostToWTM20002(com.primeton.btp.api.engine.context.IKernelServiceContext)</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;void com.psbc.pfpj.prov3502.Xmspf_Change_Feeback.HostToWTM20002(IKernelServiceContext context)&lt;/b&gt;&lt;br/&gt;&lt;p&gt; 调用委托方4002接口&#x0D;
 &lt;dl&gt;&lt;dt&gt;参数：&lt;/dt&gt;&lt;dd&gt;&lt;b&gt;HostToWTM4002&lt;/b&gt;&lt;/dd&gt;&lt;dt&gt;返回：&lt;/dt&gt;&lt;dd&gt;&lt;/dd&gt;&lt;/dl&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
</map>