<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="PSBC-CD173" category="message" create-date="2021-08-21 15:17:58" version="*******">
  <message-definition bitmap-format="Binary" bitmap-length="0" bitmap-type="unionpay" category="json" filter-null="false" id="XMWEB_COM_SubQryREQ" name="请求报文" namespace="prov3502_com.XMWEB_COM_SubQryREQ" orderly="false" xml-pack-simple-node="false" xml-pack-statement="true" nodef-json="false">
    <message-ref display-name="head" name="head" ref="common_head_req" ref-path="" seqno="0">
      <description></description>
    </message-ref>
    <group-message-item display-name="body" name="body" seqno="1" xml-prefix="">
      <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR22" display-name="子业务代码-STR22" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="sub_ope_id" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
        <ext-property/>
        <description></description>
      </message-item>
      <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR23" display-name="子业务名-STR23" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="sub_ope_nm" pad-char="0x20" seqno="1" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
        <ext-property/>
        <description></description>
      </message-item>
      <ext-property/>
    </group-message-item>
  </message-definition>
</configuration>