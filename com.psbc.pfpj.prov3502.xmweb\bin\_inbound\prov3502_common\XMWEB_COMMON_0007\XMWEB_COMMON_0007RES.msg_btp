<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="PSBC-CD173" category="message" create-date="2021-08-21 10:45:10" version="*******">
  <message-definition bitmap-format="Binary" bitmap-length="0" bitmap-type="unionpay" category="json" filter-null="false" id="XMWEB_COMMON_0007RES" name="响应报文" namespace="prov3502_common.XMWEB_COMMON_0007RES" orderly="false" xml-pack-simple-node="false" xml-pack-statement="true" nodef-json="false">
    <message-ref display-name="head" name="head" ref="common_head_res" ref-path="" seqno="0">
      <description></description>
    </message-ref>
    <group-message-item display-name="body" name="body" seqno="1" xml-prefix="">
      <repeated-item display-name="list" name="list" out-key="true" repeat-field="" repeat-times="-1" seqno="0">
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR28" display-name="入库日期-STR28" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="load_dt" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR29" display-name="流水号-STR29" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="seq_sq" pad-char="0x20" seqno="1" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR30" display-name="委托单位-STR30" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="merch_id" pad-char="0x20" seqno="2" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR45" display-name="业务代码-STR45" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="ope_cd" pad-char="0x20" seqno="3" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR32" display-name="用户号-STR32" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="pay_id" pad-char="0x20" seqno="4" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR33" display-name="卡号-STR33" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="acc_card_id" pad-char="0x20" seqno="5" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description>（1-统一柜面、2-管理端、3-委托方）</description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR34" display-name="缴费终止日-STR34" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="break_dt" pad-char="0x20" seqno="6" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR35" display-name="欠费金额-STR35" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="owe_at" pad-char="0x20" seqno="7" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR36" display-name="滞纳金-STR36" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="delay_at" pad-char="0x20" seqno="8" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR37" display-name="缴费总金额-STR37" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="tran_at" pad-char="0x20" seqno="9" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR38" display-name="缴费日期-STR38" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="tran_dt" pad-char="0x20" seqno="10" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR39" display-name="费用状态-STR39" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="txn_sta" pad-char="0x20" seqno="11" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description>状态位：0-欠费 1-正在上账 2-已缴 3-暂停 4-失败 5-清退</description>
        </message-item>
      </repeated-item>
      <ext-property/>
    </group-message-item>
  </message-definition>
</configuration>