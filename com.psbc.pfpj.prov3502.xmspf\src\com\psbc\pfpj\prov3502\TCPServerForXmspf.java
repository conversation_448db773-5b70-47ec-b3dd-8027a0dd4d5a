package com.psbc.pfpj.prov3502;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TCPServerForXmspf {
	
	private static ServerSocket ss;

	public static void main(String[] args) throws IOException {
		
		
		ss = new ServerSocket(9910);
		System.out.println("启动监听。。。");

		while(true){
			Socket socket = null;
			BufferedInputStream bis = null;
			BufferedOutputStream bos = null;
			try {
				socket = ss.accept();
				System.out.println("建立连接。。");
				bis = new  BufferedInputStream(socket.getInputStream());
				bos = new BufferedOutputStream(socket.getOutputStream());
				
				int headlen = 300;
				byte[] b_head = new byte[headlen];
				bis.read(b_head);
				String head = new String(b_head);
				System.out.println("报文内容："+head);
				
				List<String> list = getContext(head);
				System.out.println("head节点内容：");
				for(int i=0;i<list.size();i++){
					System.out.println(i+">>>>>："+list.get(i));
				}
				
				//head = head.substring(6,head.length());
				//System.out.println("报文body："+head);
				String serviceno = list.get(0).toString();
				String rspmsg = "";
				if(serviceno.equals("20002")){
					rspmsg = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><content><head><statecode>1</statecode><msg>20210816935025010003016666流水，提供的账号有误，系统中查询不到该资金监管账户!!20210816935026010003390037流水，提供的账号有误，系统中查询不到该资金监管账户!!</msg></head><body><table_account><row><detailno>20210816935020010004736655</detailno><issuccess>1</issuccess></row><row><detailno>20210816935020010028358890</detailno><issuccess>1</issuccess></row><row><detailno>20210816935021010024208890</detailno><issuccess>1</issuccess></row><row><detailno>20210816935025010003016666</detailno><issuccess>0</issuccess></row><row><detailno>20210816935026010003390037</detailno><issuccess>0</issuccess></row><row><detailno>20210816935027010004058889</detailno><issuccess>1</issuccess></row><row><detailno>20210816935029010013088891</detailno><issuccess>1</issuccess></row></table_account></body></content>";
					
				}
				/*if(head.startsWith("2101")){ 
					rspmsg = "10000.56            |***********                   |福州新中平有限公司                                                                                                                                             |6221505010005380051           |VIP金卡                                                                                                                                                 |福州古田路支行                                                                            |********* |";
					
				}else if(head.startsWith("2102")){ 
					rspmsg = "**********|";
				}else if(head.startsWith("2201")){ 
					rspmsg = "3560000.55          |***********                   |福州新中平有限公司                                                                                                                                             |**********  |福州新中平有限公司                                                                                 |62215005121101                |福州新中平有限公司                                                                                                                                             |福州古田路支行                                                                            |01|2021062302|";
				}else if(head.startsWith("2202")){ 
					rspmsg = "2021062902|";
				}else if(head.startsWith("2001")){ 
					rspmsg = "1     |10000               |935005010018434005            |谢乐聪                                                                                    |fangwuweizhi                                                                                                                                          |1234567890|";
				}else if(head.startsWith("2002")){ 
					rspmsg = "100007341220010005            |fangwuweizhi                                                                                                                                          |1234567890|";
				}else if(head.startsWith("2011")){ 
					rspmsg = "2021062701|";
				}else if(head.startsWith("2301")){ 
					rspmsg = "1     |123456789012|谢乐聪                                                                                    |fangwuweizhi                                                                                                                                          |1000000001          |2021-06-27 |beizhu1                                                     |beizhu2                                                     |beizhu3                                                     |福州古田路支行                                                                            |1234567890|";
				}else if(head.startsWith("2402")){
					rspmsg = "**********|";
				} */
				//rspmsg = String.format("%06d",rspmsg.length()) + "0000|" + rspmsg;

//rspmsg="B69941EE58E35EA9";	
			System.out.println(rspmsg);
				bos.write(rspmsg.getBytes());
				bos.flush();
			} catch (Exception e) {
				e.printStackTrace();
				continue;
			} finally {
				bis.close();
				bos.close();
				socket.close();
			}
			
		}
	}
	public static List<String> getContext(String html){
		List<String> resultList = new ArrayList<String>();
		Pattern p = Pattern.compile(">([^</]+)</");
		Matcher m = p.matcher(html);
		while(m.find()){
			resultList.add(m.group(1));
		}
		return resultList;
	}
}

