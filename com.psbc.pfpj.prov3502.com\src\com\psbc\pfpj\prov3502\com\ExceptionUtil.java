package com.psbc.pfpj.prov3502.com;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;

public class ExceptionUtil {
	/**
	 * 将错误信息打包成字符串
	 * @param ex
	 * @return
	 */
	public static String getAllExceptionInfo(Exception ex) {
		ByteArrayOutputStream out = new ByteArrayOutputStream();
		PrintStream ps = new PrintStream(out);
		ex.printStackTrace(ps);
		String ret = new String(out.toByteArray());
		ps.close();
		try {
			out.close();
		} catch (IOException e) {
			// TODO 自动生成的 catch 块
			e.printStackTrace();
		}
		//createException(ret);
		return ret;
	}

	public static void createException(String message) {
		Exception e = new Exception(message);
		e.printStackTrace();
	}

}
