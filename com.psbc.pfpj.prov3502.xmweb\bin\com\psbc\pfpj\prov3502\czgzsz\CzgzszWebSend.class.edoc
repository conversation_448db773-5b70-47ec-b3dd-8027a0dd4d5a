<map>
  <entry>
    <string>com.psbc.pfpj.prov3502.czgzsz.CzgzszWebSend.afterSend(com.primeton.btp.api.engine.context.IKernelServiceContext)</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;void com.psbc.pfpj.prov3502.czgzsz.CzgzszWebSend.afterSend(IKernelServiceContext context)&lt;/b&gt;&lt;br/&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
  <entry>
    <string>com.psbc.pfpj.prov3502.czgzsz.CzgzszWebSend.beforeSend(com.primeton.btp.api.engine.context.IKernelServiceContext)</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;int com.psbc.pfpj.prov3502.czgzsz.CzgzszWebSend.beforeSend(IKernelServiceContext context)&lt;/b&gt;&lt;br/&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
  <entry>
    <string>CzgzszWebSend</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;com.psbc.pfpj.prov3502.czgzsz.CzgzszWebSend&lt;/b&gt;&lt;br/&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
</map>