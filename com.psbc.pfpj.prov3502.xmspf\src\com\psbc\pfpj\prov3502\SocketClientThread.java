package com.psbc.pfpj.prov3502;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.net.Socket;

import com.primeton.btp.api.core.logger.ILogger;
import com.primeton.btp.api.core.logger.LoggerFactory;
import com.primeton.btp.api.transport.TransportManager;
import com.primeton.btp.spi.transport.tcp.def.TcpTransportDefinition;

public class SocketClientThread implements Runnable {
	private ILogger logger = LoggerFactory.getLogger(SocketClientThread.class);
	private byte[] reqmsg;
	
	public SocketClientThread(byte[] reqmsg){
		this.reqmsg = reqmsg;
	}
	
	public void run() {
		try {
			TcpTransportDefinition definition = (TcpTransportDefinition) TransportManager.INSTANCE.get("prov3502_xmspf").getDefinition();
			int port = definition.getPort();
			Socket socket = new Socket(GetProperties.ip, port);
			logger.info("ip========"+GetProperties.ip+"；port="+port+"；"+GetProperties.port);
			BufferedOutputStream bos = new BufferedOutputStream(socket.getOutputStream());
			BufferedInputStream bis = new BufferedInputStream(socket.getInputStream());
			bos.write(reqmsg);
			bos.flush();
			byte[] b_len = new byte[8];
			bis.read(b_len);
			String s_len = new String(b_len);
			int len = Integer.parseInt(s_len);
			byte[] rmsg = new byte[len];
			bis.read(rmsg);
			socket.close();
			String rsmsg = new String(rmsg);
			logger.debug("rsmsg:"+rsmsg);
		} catch (Exception e) {
			logger.error("调用后台服务异常",e);
		}
		
	}

}
