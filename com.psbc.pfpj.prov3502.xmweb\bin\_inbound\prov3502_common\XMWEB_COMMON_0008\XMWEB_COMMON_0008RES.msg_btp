<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="PSBC-CD173" category="message" create-date="2021-08-21 10:49:31" version="*******">
  <message-definition bitmap-format="Binary" bitmap-length="0" bitmap-type="unionpay" category="json" filter-null="false" id="XMWEB_COMMON_0008RES" name="响应报文" namespace="prov3502_common.XMWEB_COMMON_0008RES" orderly="false" xml-pack-simple-node="false" xml-pack-statement="true" nodef-json="false">
    <message-ref display-name="head" name="head" ref="common_head_res" ref-path="" seqno="0">
      <description></description>
    </message-ref>
    <group-message-item display-name="body" name="body" seqno="1" xml-prefix="">
      <repeated-item display-name="list" name="list" out-key="true" repeat-field="" repeat-times="-1" seqno="0">
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR28" display-name="委托单位代码-STR28" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="merch_id" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR29" display-name="业务代码-STR29" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="ope_cd" pad-char="0x20" seqno="1" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR30" display-name="日期-STR30" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="tran_dt" pad-char="0x20" seqno="2" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR45" display-name="委托单位名称-STR45" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="merch_name" pad-char="0x20" seqno="3" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR32" display-name="处理说明-STR32" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="proc_memo" pad-char="0x20" seqno="4" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR33" display-name="输入文件名-STR33" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="in_file_name" pad-char="0x20" seqno="5" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description>（1-统一柜面、2-管理端、3-委托方）</description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR34" display-name="输出文件名-STR34" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="out_file_name" pad-char="0x20" seqno="6" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR35" display-name="外联批次号-STR35" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="batch_id" pad-char="0x20" seqno="7" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR36" display-name="总笔数-STR36" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="tol_num" pad-char="0x20" seqno="8" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR37" display-name="总金额-STR37" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="tol_amt" pad-char="0x20" seqno="9" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR38" display-name="明细笔数-STR38" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="act_num" pad-char="0x20" seqno="10" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR39" display-name="明细金额-STR39" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="act_amt" pad-char="0x20" seqno="11" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description>状态位：0-欠费 1-正在上账 2-已缴 3-暂停 4-失败 5-清退</description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR40" display-name="处理标志-STR40" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="proc_flag" pad-char="0x20" seqno="12" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description>（成功、失败）</description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR41" display-name="错误原因-STR41" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="proc_reason" pad-char="0x20" seqno="13" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description>1、委托方文件格式错误&#xD;
2、委托方文件总笔数或总金额与明细不符&#xD;
3、入批量总控表失败&#xD;
4、入批量明细表失败&#xD;
5、向中平发批量请求失败&#xD;
6、接收批量请求应答失败（或者接收成功时响应失败的应答说明）&#xD;
7、回盘文件不存在&#xD;
8、回盘文件处理失败&#xD;
9、入欠费库失败&#xD;
10、插入交换记录表失败&#xD;
11、生成委托方文件失败&#xD;
12、发送委托方文件失败</description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR42" display-name="加载时间-STR42" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="load_time" pad-char="0x20" seqno="14" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
      </repeated-item>
      <ext-property/>
    </group-message-item>
  </message-definition>
</configuration>