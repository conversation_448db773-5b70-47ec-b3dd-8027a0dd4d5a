<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov35_xmspf.Hxmspf_20002REQ" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov35_xmspf.Hxmspf_20002REQ">
    <xs:complexType name="serviceno">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="服务编号" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="usr">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="用户名" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="pwd">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="用户密码" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="optname">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="实际操作人" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="signmsg">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="签名信息" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="head">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="serviceno" nillable="true" type="serviceno"/>
            <xs:element name="usr" nillable="true" type="usr"/>
            <xs:element name="pwd" nillable="true" type="pwd"/>
            <xs:element name="optname" nillable="true" type="optname"/>
            <xs:element name="signmsg" nillable="true" type="signmsg"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="optdate">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="记账日期时间" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="detailno">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="明细流水号" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="bankid">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="开户银行代码" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="bankname">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="开户网点名称" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="accountname">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="账户名" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="accountno">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="账户号" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="amount">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="交易金额" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="amounttype">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="金额变动类型" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="entrysourcetype">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="入账来源方式" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="balance">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="账户余额" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="obankid">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="对方账户银行" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="obankname">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="对方账户开户网点名称" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="oaccountname">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="对方账户名" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="oaccountno">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="对方账户号" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="note">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="备注" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="row">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="optdate" nillable="true" type="optdate"/>
            <xs:element name="detailno" nillable="true" type="detailno"/>
            <xs:element name="bankid" nillable="true" type="bankid"/>
            <xs:element name="bankname" nillable="true" type="bankname"/>
            <xs:element name="accountname" nillable="true" type="accountname"/>
            <xs:element name="accountno" nillable="true" type="accountno"/>
            <xs:element name="amount" nillable="true" type="amount"/>
            <xs:element name="amounttype" nillable="true" type="amounttype"/>
            <xs:element name="entrysourcetype" nillable="true" type="entrysourcetype"/>
            <xs:element name="balance" nillable="true" type="balance"/>
            <xs:element name="obankid" nillable="true" type="obankid"/>
            <xs:element name="obankname" nillable="true" type="obankname"/>
            <xs:element name="oaccountname" nillable="true" type="oaccountname"/>
            <xs:element name="oaccountno" nillable="true" type="oaccountno"/>
            <xs:element name="note" nillable="true" type="note"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="table_account">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="row" nillable="true" type="row"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="table_account" nillable="true" type="table_account"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="content">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="head"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Hxmspf_20002REQ">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="content" nillable="true" type="content"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
