package com.psbc.pfpj.prov3502;

import java.math.BigInteger;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.Signature;
import java.security.SignatureException;
import java.security.spec.ECGenParameterSpec;
import java.security.spec.InvalidKeySpecException;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;

import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.gm.GMObjectIdentifiers;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECParameterSpec;
import org.bouncycastle.jce.spec.ECPrivateKeySpec;
import org.bouncycastle.jce.spec.ECPublicKeySpec;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.util.encoders.Hex;

@SuppressWarnings("static-access")
public class SM2Utils { 

    private BouncyCastleProvider provider;
    // 获取SM2相关参数
    private X9ECParameters parameters;
    // 椭圆曲线参数规格
    private ECParameterSpec ecParameterSpec;
    // 获取椭圆曲线KEY生成器
    private KeyFactory KeyFactory;

	public SM2Utils() {
        try {
            provider = new BouncyCastleProvider();
            parameters = GMNamedCurves.getByName("sm2p256v1");
            ecParameterSpec = new ECParameterSpec(parameters.getCurve(), parameters.getG(), parameters.getN(), parameters.getH());
            KeyFactory = KeyFactory.getInstance("EC", provider);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
	
    public KeyPair generateSm2KeyPairs() throws NoSuchAlgorithmException, InvalidAlgorithmParameterException {
        final ECGenParameterSpec sm2Spec = new ECGenParameterSpec("sm2p256v1");
        // 获取椭圆曲线类型的密钥对生成器
        final KeyPairGenerator kpg = KeyPairGenerator.getInstance("EC", provider);
        SecureRandom random = new SecureRandom();
        // 获使用SM2的算法区域初始化密钥生成器
        kpg.initialize(sm2Spec);
        kpg.initialize(sm2Spec, random);
        // 获取密钥对
        KeyPair keyPair = kpg.generateKeyPair();
        return keyPair;
    }

    /*public String encode(String input, String pubKey) throws NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        // 获取SM2相关参数
        X9ECParameters parameters = GMNamedCurves.getByName("sm2p256v1");
        // 椭圆曲线参数规格
        ECParameterSpec ecParameterSpec = new ECParameterSpec(parameters.getCurve(), parameters.getG(), parameters.getN(), parameters.getH());
        // 将公钥HEX字符串转换为椭圆曲线对应的点
        ECPoint ecPoint = parameters.getCurve().decodePoint(Hex.decode(pubKey));
        // 获取椭圆曲线KEY生成器
        KeyFactory keyFactory = KeyFactory.getInstance("EC", provider);
        BCECPublicKey publicKey = (BCECPublicKey) keyFactory.generatePublic(new ECPublicKeySpec(ecPoint, ecParameterSpec));
        // 获取SM2加密器
        Cipher cipher = Cipher.getInstance("SM2", provider);
        // 初始化为加密模式
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        // 加密并编码为base64格式
        return Base64.getEncoder().encodeToString(cipher.doFinal(input.getBytes()));
    }

    public byte[] decode(String input, String priKey) throws NoSuchAlgorithmException, InvalidKeySpecException, NoSuchPaddingException, InvalidKeyException, BadPaddingException, IllegalBlockSizeException {
        // 获取SM2加密器
        Cipher cipher = Cipher.getInstance("SM2", provider);
        // 将私钥HEX字符串转换为X值
        BigInteger bigInteger = new BigInteger(priKey, 16);
        BCECPrivateKey privateKey = (BCECPrivateKey) KeyFactory.generatePrivate(new ECPrivateKeySpec(bigInteger, ecParameterSpec));
        // 初始化为解密模式
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        // 解密
        return cipher.doFinal(Base64.getDecoder().decode(input));
    }*/

    /** 
     * SM2加签
     * @param plainText 明文 
     * @param priKey 私钥 
     * @return 验签结果
     */  
    public String sign(String plainText, String priKey) throws NoSuchAlgorithmException, InvalidKeyException, InvalidKeySpecException, SignatureException {
        // 创建签名对象
        Signature signature = Signature.getInstance(GMObjectIdentifiers.sm2sign_with_sm3.toString(), provider);
        // 将私钥HEX字符串转换为X值
        BigInteger bigInteger = new BigInteger(priKey, 16);
        BCECPrivateKey privateKey = (BCECPrivateKey) KeyFactory.generatePrivate(new ECPrivateKeySpec(bigInteger, ecParameterSpec));
        // 初始化为签名模式
        signature.initSign(privateKey);
        // 传入签名字节
        signature.update(plainText.getBytes());
        // 签名
        return KeyUtils.byteToHexStrings(signature.sign());
    }

    /** 
     * SM2验签
     * @param plainText 明文 
     * @param signatureValue 签名
     * @param pubKey 公钥 
     * @return 验签结果
     */  
    public boolean verify(String plainText, String signatureValue, String pubKey) throws NoSuchAlgorithmException, InvalidKeySpecException, InvalidKeyException, SignatureException {
        // 创建签名对象
        Signature signature = Signature.getInstance(GMObjectIdentifiers.sm2sign_with_sm3.toString(), provider);
        // 将公钥HEX字符串转换为椭圆曲线对应的点
        ECPoint ecPoint = parameters.getCurve().decodePoint(Hex.decode(pubKey));
        BCECPublicKey publicKey = (BCECPublicKey) KeyFactory.generatePublic(new ECPublicKeySpec(ecPoint, ecParameterSpec));
        // 初始化为验签模式
        signature.initVerify(publicKey);
        // 传入验签字节
        signature.update(plainText.getBytes());
        // 验签
        return signature.verify(KeyUtils.hexStringsToByte(signatureValue));
    }

    
    public static void main(String[] args) throws InvalidAlgorithmParameterException, NoSuchAlgorithmException, IllegalBlockSizeException, InvalidKeyException, BadPaddingException, InvalidKeySpecException, NoSuchPaddingException, SignatureException {
        String str = "{\"accountname\":\"厦门中澳城置业有限公司\",\"accountno\":\"**************\",\"amount\":\"50008.00\",\"bankid\":\"************\",\"bankname\":\"厦门银行\",\"instructionno\":\"**************\",\"note\":\"同行\",\"oaccountname\":\"厦门中澳城置业有限公司\",\"oaccountno\":\"*****************\",\"obankid\":\"************\",\"obankname\":\"厦门银行\",\"optdate\":\"2022-03-04 17:31:04\",\"paytype\":\"1\"}";
        SM2Utils sm2Utils = new SM2Utils();
        KeyPair keyPair = sm2Utils.generateSm2KeyPairs();
        BCECPublicKey publicKey = (BCECPublicKey) keyPair.getPublic();
        BCECPrivateKey privateKey = (BCECPrivateKey) keyPair.getPrivate();

        //获取密钥
        //publicKey.setPointFormat("3059301306072A8648CE3D020106082A811CCF5501822D034200043FD8B7BA3164601F65B4ACFF4F0CB966BCDBEBDB66D32A46F3A364039A78E9F0A9592F9CC7E38A7C86BE6D59586B7E5A3E07564982AD34CC4FF74E70943084F4");
        String pubKey = new String(Hex.encode(publicKey.getQ().getEncoded(false)));
        String priKey = privateKey.getD().toString(16);
        System.out.println("pubKey:" + pubKey);
        System.out.println("priKey:" + priKey);
        pubKey = "043FD8B7BA3164601F65B4ACFF4F0CB966BCDBEBDB66D32A46F3A364039A78E9F0A9592F9CC7E38A7C86BE6D59586B7E5A3E07564982AD34CC4FF74E70943084F4";
        /*System.out.println("加密前:" + str);
        String encode = sm2.encode(str, pubKey);
        System.out.println("加密后:" + encode);
        String decode = new String(sm2.decode(encode, priKey));
        System.out.println("解密后:" + decode);
*/

        System.out.println("加签前:" + str);
        String sign = sm2Utils.sign(str, priKey);
        System.out.println("加签后:" + sign);
        sign = "3045022100CF4ED840409E002F74E2B37A18BC5C494272DC28ADB1761A9E6F5CA08CD1CB73022064A960687B20B775879D644ACBD995B0104BC5881E2F4825A4321AD4F7D7B27E";
        boolean verify = sm2Utils.verify(str, sign, pubKey);
        System.out.println("验签结果:" + verify);

    }

}
