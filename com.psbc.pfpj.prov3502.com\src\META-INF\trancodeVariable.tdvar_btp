<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" category="variable">
<!--以下情况是交易码转换所支持的规则,请根据需要,进行配置-->
<!--variable syscode ="110000160141" script="$S_OUT_TRAN_CD$=BF0100 AND $S_STRING50$=023 " fml="TX_CODE" value="811013">
    <description>支持等号规则</description>
</variable-->
<!--variable syscode ="110000160141" script="$S_OUT_TRAN_CD$=883030 AND $S_TRAN_INST_ID[1,2]$=37 AND $S_STRING1[1,3]$=054 " fml="TX_CODE" value="811013">
    <description>支持substr截取字段规则</description>
</variable-->
<!--variable syscode ="110000160141" script="$S_OUT_TRAN_CD$=BF0101 AND $S_STRING50$>025 " fml="TX_CODE" value="811014">
    <description>有其他匹配规则（目前未出现）,需要扩展类的情况</description>
	<extclass></extclass>
</variable-->
<!--variable syscode ="110000160141" script="constant" fml="OPE_CD" value="101020">
    <description>添加常量情况，系统会将fml对应的字段名和值放到报文中</description>
</variable-->

</configuration>
