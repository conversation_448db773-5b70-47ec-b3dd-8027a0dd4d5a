<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="PSBC-CD173" category="endpoint-tcp" create-date="2013-01-01 01:56:14" version="*******">
  <endpoint-tcp id="prov3502_xmspf">
    <name>prov3502_xmspf</name>
    <mode>HALF_DUPLEX</mode>
    <description>tcp接出</description>
    <host>127.0.0.1</host>
    <port>8888</port>
    <is-short-socket>true</is-short-socket>
    <data-exchange-class></data-exchange-class>
    <endpoint-caller>
      <min-size>50</min-size>
      <max-size>50</max-size>
      <increase-size>1</increase-size>
      <keep-alive-time>60</keep-alive-time>
      <checkout-timeout>5</checkout-timeout>
      <factory-class></factory-class>
    </endpoint-caller>
    <work-threads>
      <min-size>50</min-size>
      <max-size>50</max-size>
      <keep-alive-time>60</keep-alive-time>
      <queue-size>0</queue-size>
      <rejected-policy></rejected-policy>
    </work-threads>
    <work-threads-ref></work-threads-ref>
    <ext-property/>
    <ext-class></ext-class>
    <encoding>UTF-8</encoding>
    <is-record-message>true</is-record-message>
    <is-secure>null</is-secure>
    <connect-timeout>30</connect-timeout>
    <message-ending mode="0">
      <offset>0</offset>
      <length>6</length>
      <initial-bytes-to-strip>6</initial-bytes-to-strip>
      <corrected-value>0</corrected-value>
      <length-acquire-mode>2</length-acquire-mode>
    </message-ending>
    <system-field-ref></system-field-ref>
    <max-socket-size>1000</max-socket-size>
    <max-length>20480</max-length>
    <is-multi>false</is-multi>
    <transport-num>5</transport-num>
    <endpoint-num>5</endpoint-num>
    <transport-ip>127.0.0.1</transport-ip>
    <transport-port>9003</transport-port>
    <heart-beat-time>300000</heart-beat-time>
    <heart-beat-msg>com.primeton.btp.spi.endpoint.tcp.HeartBeatTest</heart-beat-msg>
    <is-route>false</is-route>
    <route-property/>
  </endpoint-tcp>
</configuration>