<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns="http://www.primeton.com/btp/cfg" author="ScottWang" category="route" create-date="" version="6.1">
	<route id="__route_hosttransservice__">
	<!--
		主机服务路由配置说明:
		policy:匹配策略
			(1)target-url（可选）:是远程机器的url地址，目前是HTTP方式，例如，http://***********/endpoint；
			(2)target-type（必选）:目标服务类型，只能是
				1. ENDPOINT，通道
			(3)target-id:目标通道编号（id属性）；
			(4)match-rule:路由匹配规则，可以多个，如果任何一个规则匹配，则路由到policy中指定的target-url(target-id)
				match-class:路由匹配判断，从com.primeton.btp.api.route.AbstractMatcher继承
				ext-property：匹配规则扩展属性

		匹配原则：
		1. 如果多个policy生效，则只路由到第一个policy，根据配置的顺序
		2. 同一个policy下的多个match-rule是或(or)的关系，如果某个match-class返回true，
		      则路由带该policy指定的target（target-url & target-id).
		-->
		<!--
		sample:
		<policy id="String">
			<name>String</name>
			<description>String</description>
			<target-url>String</target-url>
			<target-id>String</target-id>
			<target-type>String</target-type>
			<match-rules>
				<match-rule>
					<match-class>String</match-class>
					<ext-property>
						<entry description="String" value="String" key="String"/>
					</ext-property>
					<description>String</description>
				</match-rule>
			</match-rules>
		</policy>
		-->
		<policy id="DefaultEndpointRoutePolicy">
			<target-url/>
			<target-id/>
			<target-type>ENDPOINT</target-type>
			<description>默认通道路由，直接根据消息中的头信息，路由到指定的通道</description>
			<name>默认通道路由</name>
		</policy>
	</route>
</configuration>


