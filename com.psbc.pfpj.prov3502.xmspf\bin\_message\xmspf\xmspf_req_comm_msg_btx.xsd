<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="xmspf.xmspf_req_comm" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="xmspf.xmspf_req_comm">
    <xs:complexType name="serviceno">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="服务编号" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="head">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="serviceno" nillable="true" type="serviceno"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="content">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="head"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="xmspf_req_comm">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="content" nillable="true" type="content"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
