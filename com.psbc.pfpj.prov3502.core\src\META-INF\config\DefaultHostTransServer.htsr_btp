<configuration category="host-trans-server" xmlns="http://www.primeton.com/btp/cfg" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<!--
		主机服务器配置说明:
		id:				主机服务器的id标识
		name:			主机服务器的名称
		description：	主机服务器的描述信息
		timeout：		主机服务器的超时事件	单位：秒	取值范围：整型
		work-threads:	主机服务器线程池配置
			min-size:	初始化线程数大小		取值范围：大于0的整数
			max-size:	最大线程数			取值范围：大于0的整数,且不能小于min-size的值
			keep-alive-time:空闲线程存活时间	单位：秒	取值范围：大于0的整数
			queue-size:	队列大小				取值范围：整数
			rejected-policy:拒绝策略			类的全称，必须实现接口：java.util.concurrent.RejectedExecutionHandler
		work-threads-ref：工作线程引用
		ext-property:	扩展属性		example:<entry description="" value="testValue" key="test"/>
		-->
	<host-trans-server id="DefaultHostTransServer">
		<name>默认接出服务器</name>
		<description>默认的接出服务器</description>
		<timeout>-1</timeout>
		<work-threads>
			<min-size>5</min-size>
			<max-size>20</max-size>
			<keep-alive-time>100</keep-alive-time>
			<queue-size>1000</queue-size>
			<rejected-policy>java.util.concurrent.ThreadPoolExecutor$AbortPolicy</rejected-policy>
		</work-threads>
		<ext-property>
		</ext-property>
		<work-threads-ref/>
	</host-trans-server>
</configuration>