<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="XMWEB_CZGZSZ_0003.process_btx" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******">
  <nodes xsi:type="process:tStart" id="start0" name="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokePojo0</targetNode>
    </sourceConnections>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" type="end">
    <targetConnections>link1</targetConnections>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" name="outMessage" type="query">outMessage</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" nodeType="label">
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" nodeType="label">
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" id="invokePojo0" name="before" collapsed="false" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.psbc.pfpj.prov3502.czgzsz.CzgzszWebSend.beforeSend</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="context" type="query" value="com.primeton.btp.api.engine.context.IKernelServiceContext" valueType="Java" pattern="reference">__kernel_service_context</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="int" valueType="Java"/>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <figSize height="17" width="40"/>
    <node>invokePojo0</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info version="*******"/>
  <process:inputs>
    <process:input anyType="com.primeton.btp.api.engine.context.IKernelServiceContext" isArray="false" name="__kernel_service_context"/>
    <process:input isArray="false" modelType="prov3502_czgzsz.XMWEB_CZGZSZ_0003REQ.XMWEB_CZGZSZ_0003REQ" name="inMessage"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" modelType="prov3502_czgzsz.XMWEB_CZGZSZ_0003RES.XMWEB_CZGZSZ_0003RES" name="outMessage"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
</process:tBusinessLogic>
