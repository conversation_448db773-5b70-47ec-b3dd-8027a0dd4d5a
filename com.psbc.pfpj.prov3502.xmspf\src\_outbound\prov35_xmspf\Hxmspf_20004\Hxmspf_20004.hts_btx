<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="Administrator" category="outbound" create-date="2021-11-18 16:15:23" version="*******">
  <outbound id="Hxmspf_20004">
    <name>Hxmspf_20004</name>
    <description>退款通知</description>
    <timeout>0</timeout>
    <timeout-action>IGNORE</timeout-action>
    <host-trans-code></host-trans-code>
    <host-cancel-service-id></host-cancel-service-id>
    <endpoint-id>prov3502_xmspf</endpoint-id>
    <req-message-id>Hxmspf_20004REQ</req-message-id>
    <resp-message-id>Hxmspf_20004RES</resp-message-id>
    <exception-message-id>Hxmspf_20004EXCE</exception-message-id>
    <ext-property>
      <entry description="超时处理类，必须实现接口com.primeton.btp.api.engine.hosttrans.ICustomTimeoutAction" key="CUSTOM_TIMEOUT_ACTION_CLASSNAME" value=""/>
    </ext-property>
    <ext-class></ext-class>
    <is-one-way>false</is-one-way>
    <fill-type>COVER</fill-type>
    <fill-pool>true</fill-pool>
    <host-server-name></host-server-name>
    <msg2file>false</msg2file>
  </outbound>
</configuration>