package com.psbc.pfpj.prov3502;

import java.util.Iterator;
import java.util.Map;
import java.util.UUID;

import com.eos.system.annotation.Bizlet;
import com.primeton.btp.api.core.exception.BTPRuntimeException;
import com.primeton.btp.api.engine.context.IKernelServiceContext;
import com.primeton.btp.api.engine.hosttrans.HostTransServiceManager;
import com.primeton.btp.api.engine.hosttrans.HostTransServiceTemplate;
import com.primeton.btp.api.engine.hosttrans.IOutbound;
import com.primeton.btp.api.engine.hosttrans.IHostTransServiceDefinition.FillType;
import com.primeton.btp.api.engine.hosttrans.message.HostTransMessageBuilderProvider;
import com.primeton.btp.api.engine.hosttrans.message.IHostTransMessage;
import com.primeton.btp.api.engine.hosttrans.message.IHostTransMessageBuilder;
import com.primeton.btp.api.message.DataPoolUtil;
import com.primeton.btp.spi.core.si.MessageHeaderNames;
import com.primeton.btp.spi.engine.util.MessageHelper;
import com.primeton.esb.message.ITipMessagePayload;
import com.primeton.tip.org.springframework.integration.core.Message;

@Bizlet("")
public class HostServiceInvoker_202 {

	
	@Bizlet("调用主机服务")
	public static void invokeNoH(IKernelServiceContext context) {
		String hostCode = getInterCode(context);
		if (hostCode.charAt(0) == 'H') {
			hostCode = hostCode.substring(1);
		}
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		Message<?> response = synCall(hostCode, requestMessage);
		checkException(response);
		fillPool(context,hostCode,response);
	}

	@Bizlet("调用主机服务")
	public static void invoke(IKernelServiceContext context) {
		String hostCode = getInterCode(context);
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		Message<?> response = synCall(hostCode, requestMessage);
		checkException(response);
		fillPool(context,hostCode,response);
	}

	@Bizlet("调用主机服务")
	/**
	 * 自定义调用主机服务，无覆盖返回报文操作
	 * @param context
	 */
	public static Map<String, Object> MyInvoke(IKernelServiceContext context) {
		String hostCode = getInterCode(context);
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		Message<?> response = synCall(hostCode, requestMessage);
		checkException(response);
		ITipMessagePayload hPayload = (ITipMessagePayload) response.getPayload();
		Map<String, Object> hSystemHeaders = hPayload.getSystemHeaders();
		return hSystemHeaders;
	}
	
	@Bizlet("调用主机服务")
	/**
	 * 自定义调用主机服务，无覆盖返回报文操作
	 * @param context
	 */
	public static void MyInvoke1(IKernelServiceContext context) {
		String hostCode = MygetInterCode(context);
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		Message<?> response = synCall(hostCode, requestMessage);
		checkException(response);
		fillPool(context,hostCode,response);
	}
	
	private static ITipMessagePayload getRequestPayload(Message<?> requestMessage) {
		ITipMessagePayload payload = (ITipMessagePayload) requestMessage.getPayload();
		return payload;
	}

	private static String getInterCode(IKernelServiceContext context) {
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		Object data = DataPoolUtil.getData(requestMessage, "INTER_CODE");
		if(null == data) {
			throw new BTPRuntimeException("在数据池中未找到 INTER_CODE");
		}
		return "H"+ String.valueOf(data);
	}
	
	private static String MygetInterCode(IKernelServiceContext context) {
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		Object data = DataPoolUtil.getData(requestMessage, "INTER_CODE");
		if(null == data) {
			throw new BTPRuntimeException("在数据池中未找到 INTER_CODE");
		}
		return String.valueOf(data);
	}
	
	private static void fillPool(IKernelServiceContext kernelServiceContext,
			String hostServiceCode, Message<?> responseMessage) {
		Message<?> reqMsg = kernelServiceContext.getServiceRequest().getRequestMessage();
		IOutbound hostTransService = HostTransServiceManager.getInstance().getHostTransService(hostServiceCode);
		boolean fillPool = hostTransService.getHostTransServiceDefinition().isFillPool();
		if(fillPool){
			ITipMessagePayload hPayload = (ITipMessagePayload) responseMessage.getPayload();
			Map<String, Object> hSystemHeaders = hPayload.getSystemHeaders();
			ITipMessagePayload tPayload = (ITipMessagePayload) reqMsg.getPayload();
			FillType fillType = hostTransService.getHostTransServiceDefinition().getFillType();
			if(fillType.name().equalsIgnoreCase(FillType.COVER.name())){
				tPayload.getSystemHeaders().putAll(hSystemHeaders);
			}else if(fillType.name().equalsIgnoreCase(FillType.UNCOVER.name())){
				Iterator<String> iterator = hSystemHeaders.keySet().iterator();
				while(iterator.hasNext()){
					String next = iterator.next();
					if(tPayload.getSystemHeaders().containsKey(next)){
						continue;
					}
					tPayload.getSystemHeaders().put(next, hSystemHeaders.get(next));
				}
			}
		}
	}

	private static void checkException(Message<?> response) {
		if(null != response){
			String exceptionFlag = response.getHeaders().get(MessageHeaderNames.MESSAGE_EXCEPTION_FLAG, String.class);
			if(null != exceptionFlag && exceptionFlag.equals("true")){
				throw new BTPRuntimeException("调用主机服务异常",(Throwable)MessageHelper.getPayload(response));
			}
		}
	}
	
	private static Message<?> synCall(String hostTransCode, Object parma) {
		IHostTransMessage<ITipMessagePayload> request = buildRequestMessage(hostTransCode, parma);
		HostTransServiceTemplate template = new HostTransServiceTemplate();
		return template.sendAndReceive(request);
	}

	/**
	 * 构建主机服务请求消息.<br>
	 *
	 * @param hostTransCode
	 * 			主机服务码
	 * @param parma
	 * 			参数
	 * @return	主机服务请求消息
	 */
	private static IHostTransMessage<ITipMessagePayload> buildRequestMessage(String hostTransCode, Object parma){
		Message<?> message = (Message<?>)parma;
		ITipMessagePayload payload = getRequestPayload(message);
		IHostTransMessageBuilder<ITipMessagePayload> builder = HostTransMessageBuilderProvider.withPayload(payload);
		builder.setMessageID(UUID.randomUUID().toString());
		builder.setHostTransCode(hostTransCode);
		builder.copyHeadersIfAbsent(message.getHeaders());
		builder.removeHeader("$btp.request.channel.id");
		builder.removeHeader("$btp.reply.channel.id");
		builder.removeHeader("$btp.reply.error.channel.id");
		builder.removeHeader("$btp.channel.id.field.to.use");
		return builder.build();
	}
}
