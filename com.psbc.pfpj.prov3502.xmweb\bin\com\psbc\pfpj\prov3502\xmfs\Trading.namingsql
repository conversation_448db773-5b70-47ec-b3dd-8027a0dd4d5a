<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="com.psbc.pfpj.prov3502.xmfs.Trading">
    <!-- 管理端发起的中平外联交易明细查询 -->
    <select id="query_xmfs_0001" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			txn.tran_dt,
			txn.sys_tm,
			txn.tran_inst_id,
			txn.inst_term_id as tlr_id,
			txn.tran_cd,
			txn.ope_cd,
			b.ope_nm,
			txn.merch_id,
			c.prdt_nm as merch_nm,
			txn.sub_ope_cd,
			d.sub_ope_nm,
			txn.pay_id,
			txn.acc_card_id,  
			txn.tran_at,
			txn.oth_msg4_tx,
			txn.pay_cd,
			txn.tran_stat_cd,
			txn.rsp_cd,
			txn.oth_msg7_tx,
			txn.oth_msg8_tx,
			txn.oth3_fg,
			txn.oth_pr_msg3 as jysm,
			txn.oth_pr_msg2 as sys_id
		from 
			tb_int_txn_log txn
		left join  tb_ope_cd b on txn.ope_cd = b.ope_cd
		left join tb_merch_ope c on trim(txn.merch_id)=trim(c.merch_id) and trim(txn.ope_cd)=trim(c.ope_cd)
		left join tb_merch_sub_ope d on txn.ope_cd = d.ope_cd and txn.sub_ope_cd = d.sub_ope_id
		where
				txn.tran_dt between #begindt# and #enddt#
			<isNotEmpty prepend="AND" property="merch_id">txn.merch_id like '%'||#merch_id#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="ope_cd">txn.ope_cd like '%'||#ope_cd#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="str20">txn.sub_ope_cd like '%'||#str20#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="pay_id">txn.pay_id like '%'||#pay_id#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="card_no">txn.acc_card_id like '%'||#card_no#||'%'</isNotEmpty>
		union 
		select 
			dtl.tran_dt,
			dtl.last_modify_tm,
			dtl.tran_inst_id,
			ctrl.last_modify_tlr_id,
			ctrl.tran_cd,
			dtl.ope_cd,
			b.ope_nm,
			dtl.merch_id,
			c.prdt_nm as merch_nm,
			dtl.corp_cd,
			d.sub_ope_nm,
			dtl.pay_id,
			dtl.acc_id,  
			dtl.tran_at,
			null as invoiceno,
			null as checkcd,
			ctrl.proc_fg,
			dtl.ans_cd,
			dtl.ans_tx,
			null as msg8,
			null as oth3_fg,
			null as jysm,
			null as sys_id
		from 
			tb_batch_tran_dtl dtl
		join  tb_batch_tran_ctrl ctrl on dtl.batch_id = ctrl.batch_id
		left join  tb_ope_cd b on dtl.ope_cd = b.ope_cd
		left  join tb_merch_ope c on  trim(dtl.merch_id)=trim(c.merch_id) and  trim(dtl.ope_cd) = trim(c.ope_cd)
		left join tb_merch_sub_ope d on dtl.ope_cd = d.ope_cd and dtl.corp_cd = d.sub_ope_id
		where
				dtl.tran_dt between #begindt# and #enddt#
			<isNotEmpty prepend="AND" property="merch_id">dtl.merch_id like '%'||#merch_id#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="ope_cd">dtl.ope_cd like '%'||#ope_cd#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="str20">dtl.CORP_CD like '%'||#str20#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="pay_id">dtl.pay_id like '%'||#pay_id#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="card_no">dtl.ACC_ID like '%'||#card_no#||'%'</isNotEmpty>
		order by 1 desc
	</select>
    <!-- 管理端发起的中平外联交易明细查询SQL备份 -->
    <select id="query_xmfs_0001_BAK20211125" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			txn.tran_dt,
			txn.sys_tm,
			txn.tran_inst_id,
			txn.tlr_id,
			txn.tran_cd,
			txn.ope_cd,
			b.ope_nm,
			txn.merch_id,
			c.merch_nm,
			txn.sub_ope_cd,
			d.sub_ope_nm,
			txn.pay_id,
			txn.acc_card_id,  
			txn.tran_at,
			txn.oth_msg4_tx,
			txn.pay_cd,
			txn.tran_stat_cd,
			txn.rsp_cd,
			txn.oth_msg7_tx,
			txn.oth_msg8_tx
		from 
			tb_int_txn_log txn
		left join 
			tb_ope_cd b
		on
			txn.ope_cd = b.ope_cd
		left join
			tb_merch_bas_info c
		on 
			txn.merch_id = c.merch_id
		left join
			tb_merch_sub_ope d
		on
			txn.ope_cd = d.ope_cd
			and txn.sub_ope_cd = d.sub_ope_id
		where
				txn.tran_dt between #begindt# and #enddt#
			<isNotEmpty prepend="AND" property="merch_id">txn.merch_id like '%'||#merch_id#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="ope_cd">txn.ope_cd like '%'||#ope_cd#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="str20">txn.sub_ope_cd like '%'||#str20#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="pay_id">txn.pay_id like '%'||#pay_id#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="card_no">txn.acc_card_id like '%'||#card_no#||'%'</isNotEmpty>
		union 
		select 
			dtl.tran_dt,
			dtl.last_modify_tm,
			dtl.tran_inst_id,
			ctrl.last_modify_tlr_id,
			ctrl.tran_cd,
			dtl.ope_cd,
			b.ope_nm,
			dtl.merch_id,
			c.merch_nm,
			dtl.corp_cd,
			d.sub_ope_nm,
			dtl.pay_id,
			dtl.acc_id,  
			dtl.tran_at,
			null as invoiceno,
			null as checkcd,
			ctrl.proc_fg,
			dtl.ans_cd,
			dtl.ans_tx,
			null as msg8
		from 
			tb_batch_tran_dtl dtl
		join 
			tb_batch_tran_ctrl ctrl
		on
			dtl.batch_id = ctrl.batch_id
		left join 
			tb_ope_cd b
		on
			dtl.ope_cd = b.ope_cd
		left join
			tb_merch_bas_info c
		on 
			dtl.merch_id = c.merch_id
		left join
			tb_merch_sub_ope d
		on
			dtl.ope_cd = d.ope_cd
			and dtl.corp_cd = d.sub_ope_id
		where
				dtl.tran_dt between #begindt# and #enddt#
			<isNotEmpty prepend="AND" property="merch_id">dtl.merch_id like '%'||#merch_id#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="ope_cd">dtl.ope_cd like '%'||#ope_cd#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="str20">dtl.CORP_CD like '%'||#str20#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="pay_id">dtl.pay_id like '%'||#pay_id#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="card_no">dtl.ACC_ID like '%'||#card_no#||'%'</isNotEmpty>
		order by 1 desc
	</select>
    <!-- 管理端发起的非税解缴金库分类汇总查询 -->
    <select id="query_xmfs_0002_00" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select
			null as subj_id,
			null as subj_nm,
			unit_no,
			unit_name,
			item_id,
			item_nm,
			sum(cast(pay_amt as numeric)) as sum_pay_amt,
			sum(cast(delary_amt as numeric)) as sum_delary_amt,
			sum(cast(pay_amt as numeric) + cast(delary_amt as numeric)) as sum_tran_amt,
			subj_id as mark
		from
			tb_nontax_pay_dtl
		where
			tran_dt between #begindt# and #enddt#
			<isNotEmpty prepend="AND" property="budget_level">budget_level = #budget_level#</isNotEmpty>
        <isNotEmpty prepend="AND" property="merch_id">merch_id = #merch_id#</isNotEmpty>
        <isNotEmpty prepend="AND" property="ope_cd">ope_cd = #ope_cd#</isNotEmpty>
		group by
			subj_id,
			subj_nm,
			unit_no,
			unit_name,
			item_id,
			item_nm
		union all
		select 
			subj_id,
			subj_nm,
			'共' || count(*) ||'笔',
			null,
			null,
			null,
			sum(cast(pay_amt as numeric)) as sum_pay_amt,
			sum(cast(delary_amt as numeric)) as sum_delary_amt,
			sum(cast(pay_amt as numeric) + cast(delary_amt as numeric)) as sum_tran_amt,
			subj_id
		from
			tb_nontax_pay_dtl
		where
			tran_dt between #begindt# and #enddt#
			<isNotEmpty prepend="AND" property="budget_level">budget_level = #budget_level#</isNotEmpty>
        <isNotEmpty prepend="AND" property="merch_id">merch_id = #merch_id#</isNotEmpty>
        <isNotEmpty prepend="AND" property="ope_cd">ope_cd = #ope_cd#</isNotEmpty>
		group by
			subj_id,
			subj_nm
		union all
		select 
			'总计',
			'共' || count(*) ||'笔',
			null,
			null,
			null,
			null,
			coalesce(sum(cast(pay_amt as numeric)), '0') as sum_pay_amt,
			coalesce(sum(cast(delary_amt as numeric)), '0') as sum_delary_amt,
			coalesce(sum(cast(pay_amt as numeric) + cast(delary_amt as numeric)), '0') as sum_tran_amt,
			null
		from
			tb_nontax_pay_dtl
		where
			tran_dt between #begindt# and #enddt#
			<isNotEmpty prepend="AND" property="budget_level">budget_level = #budget_level#</isNotEmpty>
        <isNotEmpty prepend="AND" property="merch_id">merch_id = #merch_id#</isNotEmpty>
        <isNotEmpty prepend="AND" property="ope_cd">ope_cd = #ope_cd#</isNotEmpty>
		order by 
			mark,
			item_id
		desc
	</select>
    <select id="query_xmfs_0002" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			sum(pay_num) as "total_count",
			sum(pay_amt) as "total_pay_amt",
			sum(delary_amt) as "total_delary_amt",
			sum(sum_amt) as "total_sum_amt"
		from
			(select
				merch_id,
				ope_cd,
				tran_dt,
				budget_level,
				item_id,
				item_nm,
				case when cast(pay_num as bigint) = 0 then 1 else cast(pay_num as bigint) end,
				cast(pay_amt as bigint),
				cast(delary_amt as bigint),
				(cast(pay_amt as bigint)+cast(delary_amt as bigint)) as "sum_amt"
			from
				tb_nontax_pay_dtl
			where
					tran_dt between #begindt# and #enddt#
				<isNotEmpty prepend="AND" property="merch_id">merch_id = #merch_id#</isNotEmpty>
        <isNotEmpty prepend="AND" property="ope_cd">ope_cd = #ope_cd#</isNotEmpty>
        <isNotEmpty prepend="AND" property="budget_level">budget_level = #budget_level#</isNotEmpty>)
			as "sub_table"
	</select>
    <!-- 管理端发起的非税解缴金库分类汇总查询 -->
    <select id="query_xmfs_0003" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select
			d.merch_id,
			d.budget_type,
			d.filler_1,
			sum(cast(pay_amt as bigint)) as pay_amt,
			sum(cast(delary_amt as bigint)) as delary_amt,
			sum(round((cast(pay_amt as numeric) + cast(delary_amt as numeric)), 0)) as "sum_amt",
			sum(round(cast(substring(share_rate,1,5)  as numeric)/10000 * (cast(pay_amt as numeric)+cast(delary_amt as numeric)), 0)) as central,
			sum(round(cast(substring(share_rate,6,5)  as numeric)/10000 * (cast(pay_amt as numeric)+cast(delary_amt as numeric)), 0)) as province,
			sum(round(cast(substring(share_rate,11,5) as numeric)/10000 * (cast(pay_amt as numeric)+cast(delary_amt as numeric)), 0)) as city,
			sum(round(cast(substring(share_rate,16,5) as numeric)/10000 * (cast(pay_amt as numeric)+cast(delary_amt as numeric)), 0)) as district,
			sum(round(cast(substring(share_rate,21,5) as numeric)/10000 * (cast(pay_amt as numeric)+cast(delary_amt as numeric)), 0)) as street
		from
			tb_nontax_pay_dtl d
		where 
			d.ope_cd = '104010' and d.merch_id != '350200050289'
			and d.tran_dt between #begindt# and #enddt#
		group by 
			d.merch_id,
			d.budget_type,
			d.filler_1</select>
    <!-- 查询委托单位名称 -->
    <select id="query_xmfs_merch_name" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select
			split_part(prdt_nm,'-',1) as prdt_nm
		from
			tb_merch_ope
		where 
			merch_id = #merch_id#</select>
</sqlMap>