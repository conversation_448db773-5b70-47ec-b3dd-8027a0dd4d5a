<map>
  <entry>
    <string>com.psbc.pfpj.prov3502.HostServiceInvoker_202.MyInvoke1(com.primeton.btp.api.engine.context.IKernelServiceContext)</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;void com.psbc.pfpj.prov3502.HostServiceInvoker_202.MyInvoke1(IKernelServiceContext context)&lt;/b&gt;&lt;br/&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
  <entry>
    <string>HostServiceInvoker_202</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;com.psbc.pfpj.prov3502.HostServiceInvoker_202&lt;/b&gt;&lt;br/&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
  <entry>
    <string>com.psbc.pfpj.prov3502.HostServiceInvoker_202.invoke(com.primeton.btp.api.engine.context.IKernelServiceContext)</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;void com.psbc.pfpj.prov3502.HostServiceInvoker_202.invoke(IKernelServiceContext context)&lt;/b&gt;&lt;br/&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
  <entry>
    <string>com.psbc.pfpj.prov3502.HostServiceInvoker_202.MyInvoke(com.primeton.btp.api.engine.context.IKernelServiceContext)</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;Map&lt;String,Object&gt; com.psbc.pfpj.prov3502.HostServiceInvoker_202.MyInvoke(IKernelServiceContext context)&lt;/b&gt;&lt;br/&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
  <entry>
    <string>com.psbc.pfpj.prov3502.HostServiceInvoker_202.invokeNoH(com.primeton.btp.api.engine.context.IKernelServiceContext)</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;void com.psbc.pfpj.prov3502.HostServiceInvoker_202.invokeNoH(IKernelServiceContext context)&lt;/b&gt;&lt;br/&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
</map>