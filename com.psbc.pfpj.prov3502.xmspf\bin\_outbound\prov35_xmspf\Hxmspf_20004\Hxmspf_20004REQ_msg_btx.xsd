<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov35_xmspf.Hxmspf_20004REQ" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov35_xmspf.Hxmspf_20004REQ">
    <xs:complexType name="serviceno">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="服务编号" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="usr">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="用户名" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="pwd">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="用户密码" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="optname">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="实际操作人" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="signmsg">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="签名信息" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="head">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="serviceno" nillable="true" type="serviceno"/>
            <xs:element name="usr" nillable="true" type="usr"/>
            <xs:element name="pwd" nillable="true" type="pwd"/>
            <xs:element name="optname" nillable="true" type="optname"/>
            <xs:element name="signmsg" nillable="true" type="signmsg"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="instructionno">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="指令流水号" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="optdate">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="退款时间" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="row">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="instructionno" nillable="true" type="instructionno"/>
            <xs:element name="optdate" nillable="true" type="optdate"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="table_account">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="row" nillable="true" type="row"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="table_account" nillable="true" type="table_account"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="content">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="head"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Hxmspf_20004REQ">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="content" nillable="true" type="content"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
