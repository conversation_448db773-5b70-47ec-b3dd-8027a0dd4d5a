<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_xmspf.H350200940109_20002RES" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_xmspf.H350200940109_20002RES">
    <xs:complexType name="H350200940109_20002RES">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="RESPCODE" nillable="true" type="xs:string"/>
            <xs:element name="RESP_INFO" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
