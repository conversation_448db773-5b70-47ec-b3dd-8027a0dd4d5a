<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_czgzsz.XMWEB_CZGZSZ_0003RES" xmlns:msg_body_0003R="prov3502_czgzsz.msg_body_0003R" xmlns:msg_head_R="prov3502_czgzsz.msg_head_R" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_czgzsz.XMWEB_CZGZSZ_0003RES">
    <xs:import namespace="prov3502_czgzsz.msg_head_R" schemaLocation="../prov3502_czgzsz/msg_head_R_msg_btx.xsd"/>
    <xs:import namespace="prov3502_czgzsz.msg_body_0003R" schemaLocation="../prov3502_czgzsz/msg_body_0003R_msg_btx.xsd"/>
    <xs:complexType name="XMWEB_CZGZSZ_0003RES">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="msg_head_R:msg_head_R"/>
            <xs:element name="body" nillable="true" type="msg_body_0003R:msg_body_0003R"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
