<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="com.psbc.pfpj.prov3502.xmpldsf.xmpldsf">
    <!-- 查询当日该用户号是否签约 -->
    <select id="query_record" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select 
			tran_dt
		from 
			tb_3502_xmykt_record
		where
			record_id = 'URA'
			and merch_id = #merch_id#
			and ope_cd = #ope_cd#
			and user_id = #user_id#
			and acc_card_id = #old_card_id#</select>
    <!-- 查询当日该用户号是否签约 -->
    <select id="query_commi_info" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select 
			merch_id,
			ope_cd,
			pay_id,
			sub_key,
			corp_cd,
			payer_id,
			acc_id,
			acc_card_id,
			acc_name,
			acc_bank_flag,
			card_pk_fg,
			sign_name,
			phone_id,
			commi_dt,
			effect_dt,
			time_stamp,
			stat_cd,
			mark1,
			paper_id
		from 
			tb_pay_commi_info
		where
			merch_id = #merch_id#
			and ope_cd = #ope_cd#
			and pay_id = #pay_id#
			and acc_card_id = #acc_card_id#</select>
    <!-- 修改签约表 -->
    <update id="update_commi_info" parameterClass="java.util.HashMap">update 
    		tb_pay_commi_info
    	set
    		stat_cd = #stat_cd#
    	where
			merch_id = #merch_id#
			and ope_cd = #ope_cd#
			and pay_id = #pay_id#
			and acc_card_id = #acc_card_id#</update>
    <!-- 插入签约表 -->
    <insert id="add_commi_info" parameterClass="java.util.HashMap">insert into tb_pay_commi_info(
			merch_id,
			ope_cd,
			pay_id,
			sub_key,
			corp_cd,
			payer_id,
			acc_id,
			acc_card_id,
			acc_name,
			acc_bank_flag,
			card_pk_fg,
			sign_name,
			phone_id,
			commi_dt,
			effect_dt,
			time_stamp,
			stat_cd,
			mark1,
			paper_id
    	)values(
    		#merch_id#,
			#ope_cd#,
			#pay_id#,
			#sub_key#,
			#corp_cd#,
			#payer_id#,
			#acc_card_id#,
			#acc_card_id#,
			#acc_name#,
			#acc_bank_flag#,
			#card_pk_fg#,
			#sign_name#,
			#phone_id#,
			#commi_dt#,
			#effect_dt#,
			#time_stamp#,
			#stat_cd#,
			#mark1#,
			#paper_id#
    	)</insert>
    <!-- 修改银联交换记录表 -->
    <update id="update_record" parameterClass="java.util.HashMap">update 
    		tb_3502_xmykt_record
    	set
    		acc_card_id = #new_card_id#
    	where
			record_id = 'URA'
			and merch_id = #merch_id#
			and ope_cd = #ope_cd#
			and user_id = #user_id#
			and acc_card_id = #old_card_id#</update>
    <!-- 查询签约失败记录 -->
    <select id="query_record_fail" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select 
			merch_id,
			ope_cd,
			user_id,
			cust_name,
			host_rsp
		from 
			tb_3502_xmykt_record
		where
			record_id = 'URA'
			and merch_id = #merch_id#
			and ope_cd = #ope_cd#
			and tran_dt between #start_date# and #end_date#
			and host_rsp = '0'</select>
</sqlMap>