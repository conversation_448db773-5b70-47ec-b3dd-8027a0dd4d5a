<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_xmspf.xmspf_20001RES" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xmspf_msghead_res="xmspf.xmspf_msghead_res" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_xmspf.xmspf_20001RES">
    <xs:import namespace="xmspf.xmspf_msghead_res" schemaLocation="../xmspf/xmspf_msghead_res_msg_btx.xsd"/>
    <xs:complexType name="accountno">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="账号" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ismatch">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="是否匹配" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="accountno" nillable="true" type="accountno"/>
            <xs:element name="ismatch" nillable="true" type="ismatch"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="content">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="xmspf_msghead_res:xmspf_msghead_res"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="xmspf_20001RES">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="content" nillable="true" type="content"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
