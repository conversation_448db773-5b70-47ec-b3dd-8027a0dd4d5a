<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="com.psbc.pfpj.prov3502.xmspf.spfyszjjg">
    <!-- 查询机构号是否存在-->
    <select id="select_inst1" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select * from tb_inst_info where inst_id=#orgcode#</select>
    <!-- 查询机构号是否是登录机构本级或下级机构-->
    <select id="select_inst2" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">with recursive r as (
			select * from tb_inst_info where inst_id=#orgcode#
			union all 
			select t1.* from tb_inst_info t1,r where t1.parent_inst_id = r.inst_id
		 )
		 select inst_id,inst_nm from r where r.inst_id=#inst_no#</select>
    <!-- 密钥查询 -->
    <select id="query_pub_key" parameterClass="java.util.HashMap" resultClass="java.lang.String">SELECT 
			pub_key
		FROM
			tb_keys
		WHERE unit_code = '************'
			AND unit_type = '98';</select>
    <!-- 密钥存储 -->
    <insert id="insert_tb_keys" parameterClass="java.util.HashMap">INSERT INTO tb_keys 
		(	unit_code, 
			unit_type,
			pub_key
		) 
		VALUES 
		(
			'************', 
			'98', 
			#pub_key#
		);</insert>
    <!-- 密钥修改 -->
    <update id="update_tb_keys" parameterClass="java.util.HashMap">UPDATE 
			tb_keys 
		SET 
			pub_key = #pub_key#
		WHERE
			unit_code = '************'
			AND unit_type = '98';</update>
    <!-- 账户设立-->
    <insert id="insert_tb_zjjg_acct_info" parameterClass="java.util.HashMap">insert into tb_zjjg_acct_info(
			merch_id,
			ope_cd,
			trans_type,
			cpab_acc_id,
			open_brh_id,
			brh_name,
			pbc_brh_id,
		    acct_nm,
		    tran_dt,
		    peer_acc_id,
		    dtl_cstm_nm,
		    txn_brh_id,
		    tran_time,
		    tlr_id)
		values(
			#merchid#,
			#opecd#,
			'zzg',
			#account#,
			#inst_no#,
			#inst_name#,
			#unit_code#,
			#acct_name#,
			#tx_date#,
			#account_no#,
			#str31#,
			#orgcode#,
			#tran_time#,
			#empcode#)</insert>
    <!-- 查询账号-->
    <select id="select_account" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		SELECT t.merch_id as merchid,
		       t.ope_cd as opecd,
		       t.cpab_acc_id as account,
		       t.open_brh_id as inst_no,
		       t.brh_name as inst_name,
			   t.pbc_brh_id as unit_code,
			   t.acct_nm as acct_name,
			   t.tran_dt as tx_date,
			   t.peer_acc_id as account_no,
			   t.dtl_cstm_nm as str31
		FROM tb_zjjg_acct_info t
		where t.trans_type='zzg'	
         and t.merch_id = #merchid#
         and t.ope_cd = #opecd#
         <isNotEmpty property="account">and t.cpab_acc_id = #account#</isNotEmpty>
        <isNotEmpty property="inst_no">and t.open_brh_id = #inst_no#</isNotEmpty>
    </select>
    <!-- 查询账号-->
    <select id="select_account1" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">SELECT t.*
		FROM tb_zjjg_acct_info t
		where t.trans_type='zzg'	
         and t.merch_id = #merchid#
         and t.ope_cd = #opecd#
         and t.cpab_acc_id = #account#</select>
    <!-- 账户删除-->
    <delete id="delete" parameterClass="java.util.HashMap">delete from tb_zjjg_acct_info
	   where trans_type='zzg'	
         and merch_id = #merchid#
         and ope_cd = #opecd#
         and cpab_acc_id = #account#</delete>
    <!-- 监管账户支付指令查询-->
    <select id="select_tb_zjjg_tran_info" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			t.tran_time,
			t.tran_sq,
			t.tran_fg,
			(case when t.oth_msg1_tx = '1' then '支付待复核' when t.oth_msg1_tx = '3' then '退款待复核' else (case when t.tran_fg='0' then '待支付' when t.tran_fg='1' then '已支付' else '退款' end) end) as tranfg,
			t.oth_msg1_tx,
			t.oth_msg2_tx,
			t.open_brh_id,
			t.open_brh_nm,
			t.cpab_acc_id,
			t.acct_nm,
			(case when t.vch_type='1' then '支取' when t.vch_type='2' then '监管行互转' else '退款' end) as vch_type,
			t.tran_at,
			t.peer_brh_id,
			t.peer_brh_nm,
			t.peer_acc_id,
			t.peer_acc_nm,
			t.aptn_tx,
			t.actxt_tx,
			to_char(t.last_update_time, 'yyyymmddhh24miss') as last_update_time,
			t.txn_brh_id,
			i.inst_id as brh_id,
			i.inst_nm as brh_nm,
			t.tlr_id,
			t.oth_msg4_tx,
			t.oth_msg5_tx
		from tb_zjjg_tran_info t
		left join tb_inst_info i on t.txn_brh_id = i.inst_id
		where t.TRANS_TYPE='zzg'
		<!--and t.txn_brh_id in (
			with recursive r as (
				select * from tb_inst_info where inst_id=#orgcode#
				union all 
				select t1.* from tb_inst_info t1,r where t1.parent_inst_id = r.inst_id)
			 select inst_id from r) -->
		and t.merch_id = #merch_id#
		and t.ope_cd = #ope_cd#
		<isEqual compareValue="0" property="busi_type">and t.tran_fg in ('0','1','3')</isEqual>
        <isEqual compareValue="1" property="busi_type">and t.tran_fg not in ('0','1','3')</isEqual>
        <isNotEmpty property="tran_sq">and t.tran_sq = #tran_sq#</isNotEmpty>
        <isNotEmpty property="bgn_date">
			and t.tran_dt <![CDATA[ >= #bgn_date# ]]></isNotEmpty>
        <isNotEmpty property="end_date">
			and t.tran_dt <![CDATA[ <= #end_date# ]]></isNotEmpty>
		order by
			t.tran_fg,
			last_update_time
	</select>
    <!-- 目标账户是否在该委托单位下建立监管账户-->
    <select id="select_tb_zjjg_acct_info" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select * from tb_zjjg_acct_info 
		where merch_id = #merch_id#
		  and ope_cd = #ope_cd#
		  and trans_type = 'zzg'
		  and cpab_acc_id = #account#</select>
    <!-- 查询监管账户-->
    <select id="select_cpab_acc_id" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select * from tb_zjjg_acct_info 
		where merch_id = #merch_id# 
		  and ope_cd = #ope_cd#
		  and trans_type='zzg'</select>
    <!-- 监管账户支付指令查询-->
    <select id="select_tb_zjjg_tran_info1" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select * from tb_zjjg_tran_info 
		where merch_id = #merch_id#
		  and ope_cd = #ope_cd#
		  and trans_type = 'zzg'
		  and tran_dt <![CDATA[ >= #bgn_date# ]]>
		  and tran_dt <![CDATA[ <= #end_date# ]]><isNotEmpty property="acccount">and cpab_acc_id = #acccount#</isNotEmpty>
    </select>
</sqlMap>