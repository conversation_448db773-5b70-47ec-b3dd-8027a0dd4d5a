package com.psbc.pfpj.prov3502.core;

import com.primeton.btp.api.core.exception.BTPRuntimeException;
import com.primeton.btp.api.endpoint.IEndpointCaller;
import com.primeton.btp.api.endpoint.IEndpointCallerFactory;
import com.primeton.btp.api.endpoint.http.IHttpEndpointDefinition;
import com.primeton.btp.api.endpoint.http.IHttpEndpointDefinition.Method;
import com.primeton.btp.api.endpoint.http.exception.ExceptionCodes;
import com.primeton.btp.spi.endpoint.AbstractEndpointCallerFactory;
import com.primeton.btp.spi.endpoint.http.AbstractHttpEndpoint;

public class MyHttpDoubleIPCallerFactory extends AbstractEndpointCallerFactory
		implements IEndpointCallerFactory {

	public IEndpointCaller create() {
		IHttpEndpointDefinition definition = (IHttpEndpointDefinition) getEndpoint().getDefinition();
		Method method = definition.getMethod();
		switch (method) {		
		case POST:
			return new MyHttpDIPPostCaller((AbstractHttpEndpoint) getEndpoint());
		}
		throw new BTPRuntimeException(ExceptionCodes.CREATE_HTTP_ENDPOINT_CALLER_FAILED, new Object[] { definition.getId() }, "The http method '" + method + "' is not supported.");
	
	}

}
