<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_czgzsz.XMWEB_CZGZSZ_0004REQ" xmlns:msg_body_0004Q="prov3502_czgzsz.msg_body_0004Q" xmlns:msg_head_Q="prov3502_czgzsz.msg_head_Q" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_czgzsz.XMWEB_CZGZSZ_0004REQ">
    <xs:import namespace="prov3502_czgzsz.msg_head_Q" schemaLocation="../prov3502_czgzsz/msg_head_Q_msg_btx.xsd"/>
    <xs:import namespace="prov3502_czgzsz.msg_body_0004Q" schemaLocation="../prov3502_czgzsz/msg_body_0004Q_msg_btx.xsd"/>
    <xs:complexType name="XMWEB_CZGZSZ_0004REQ">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="msg_head_Q:msg_head_Q"/>
            <xs:element name="body" nillable="true" type="msg_body_0004Q:msg_body_0004Q"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
