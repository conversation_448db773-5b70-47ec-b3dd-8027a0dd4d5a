/**
 * 
 */
package com.psbc.pfpj.prov3502;

import java.io.BufferedInputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import sun.nio.cs.ext.Big5;

import com.eos.common.transaction.ITransactionManager;
import com.eos.common.transaction.TransactionManagerFactory;
import com.eos.system.annotation.Bizlet;
import com.pfpj.foundation.database.DatabaseExt;
import com.primeton.btp.api.core.logger.ILogger;
import com.primeton.btp.api.core.logger.LoggerFactory;
import com.primeton.btp.api.engine.context.IKernelServiceContext;
import com.primeton.btp.api.message.DataPoolUtil;
import com.primeton.btp.com.alibaba.fastjson.JSON;
import com.primeton.btp.com.alibaba.fastjson.JSONArray;
import com.primeton.btp.com.alibaba.fastjson.JSONObject;
import com.primeton.tip.org.springframework.integration.core.Message;
import com.psbc.pfpj.filesrvgw.client.FileSrvConfig;
import com.psbc.pfpj.filesrvgw.client.FileTrans;
import com.psbc.pfpj.prov3502.xmweb.LogTableDAO;
import com.psbc.pfpj.prov3502.xmweb.NoneType;



/**
 * <AUTHOR>
 * @date 2020-8-24 09:12:33
 * 
 */
@Bizlet("厦门商品房")
public class XmspfPaydeal {

	private static ILogger log = LoggerFactory.getLogger(XmspfPaydeal.class);

	@Bizlet("统一柜面发起交易前处理")
	public static String PaydealPub(IKernelServiceContext context) {
		// 获取容器
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		String message_id = (String) requestMessage.getHeaders().get("$btp.message.id");
		log.debug("["+message_id+"] For循环进行Map集合遍历--->");
		Map<String, Object> mapData = DataPoolUtil.getDataPool(requestMessage);
		for (Map.Entry<String, Object> m : mapData.entrySet()) {
			log.debug("["+message_id+"] " + m.getKey() + ";Value:" + m.getValue());
		}
		String tran_type = "";
		try {
			String merch_id = (String)DataPoolUtil.getData(requestMessage,"MERCH_ID");
			log.info("委托单位代码merch_id=" + merch_id);
			String ope_cd = (String)DataPoolUtil.getData(requestMessage,"OPE_CD");
			log.info("业务代码ope_cd=" + ope_cd);
		
			//后台数据转存
			String MESSAGE_MARK = String.valueOf(DataPoolUtil.getData(requestMessage, "MESSAGE_MARK"));
			String DECODE_IP = String.valueOf(DataPoolUtil.getData(requestMessage, "DECODE_IP"));
			String INTER_CODE = String.valueOf(DataPoolUtil.getData(requestMessage, "INTER_CODE"));
			DataPoolUtil.addData(requestMessage, "REMARKS", MESSAGE_MARK+"|"+DECODE_IP+"|"+INTER_CODE);
			DataPoolUtil.addData(requestMessage, "SND_RANG", "0");
			String outsys_tx_code = (String) DataPoolUtil.getData(requestMessage, "OUTSYS_TX_CODE");
			String qry_type = (String) DataPoolUtil.getData(requestMessage, "QRY_TYPE"); //功能代码 
			String class_id = (String) DataPoolUtil.getData(requestMessage, "CLASS_ID");//交易类型
			String class_code = "";//交易代码
			String str7 = (String) DataPoolUtil.getData(requestMessage, "STR7");
			JSONArray resultJsonArray = JSON.parseArray(str7);
			if(resultJsonArray != null) {
				JSONObject resultJsonObject = (JSONObject) resultJsonArray.get(0);
				class_code = (String) resultJsonObject.get("CLASS_CODE");
			} else {
				class_code = (String) DataPoolUtil.getData(requestMessage, "CLASS_CODE");
			}
			log.info("[01]支付指令查询/[02]支付指令状态维护/[03]退款通知/[04]支付指令复核");
			log.info("功能代码 ="+qry_type + " 交易类型"+class_id);
			String orgcode = (String) DataPoolUtil.getData(requestMessage, "INST_NO");//机构号
			log.info("机构号 ="+orgcode );
			
			if("01".equals(qry_type)){
				tran_type = "支付指令查询";
				log.info("xmspf-01-支付指令查询");
				if("02".equals(class_id)){
					log.info("xmspf-0102-支付指令明细查询");
					String pay_id=DataPoolUtil.getData(requestMessage, "BILL_NO")==null ? "":DataPoolUtil.getData(requestMessage, "BILL_NO").toString();
					if(pay_id.length()==0){
						log.info("支付指令不能为空");
						DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令不能为空");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令不能为空");
						return "0";
					}
					List<HashMap<String, Object>> resultList = new ArrayList<HashMap<String,Object>>();	
					HashMap<String,Object> hmp = new HashMap<String,Object>();
					hmp.put("merch_id",merch_id);
					hmp.put("ope_cd",ope_cd);
					hmp.put("orgcode",orgcode);
					hmp.put("tran_sq",pay_id);
					log.info("hmp="+hmp);
					Object[] result_datas = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.select_zjjg_tran_detail", hmp);
					log.info("length="+result_datas.length);
					if (result_datas.length > 0){
						Map<String,Object> result_data = (Map<String, Object>) result_datas[0];
						HashMap<String,Object> resutl = new HashMap<String, Object>();
						resutl.put("RCV_TIME",result_data.get("tran_time"));//时间	20
						resutl.put("PAY_ID",result_data.get("tran_sq"));//指令流水号	30
						resutl.put("INST_NO",orgcode);//中平网点机构代码	20
						resutl.put("INST_NAME","");//中平网点机构名称	100
						resutl.put("OPEN_UNIT_CODE", result_data.get("open_brh_id"));//开户行代码	50
						resutl.put("UNIT_NAME", result_data.get("open_brh_nm"));//开户网点名称	100
						resutl.put("ACCT_NAME",result_data.get("acct_nm"));//账户名	100
						resutl.put("ACCOUNT", result_data.get("cpab_acc_id"));//账户号	40
						resutl.put("BUSI_TYPE",result_data.get("vch_type"));//支付类型	1：同行 2：跨行	1
						resutl.put("SETTL_AMT",result_data.get("tran_at"));//结算金额	
						resutl.put("ISS_ORG_NO", result_data.get("peer_brh_id"));//目标账户开户行代码	50
						resutl.put("STR33", result_data.get("peer_brh_nm"));//目标账户开户网点名称	100
						resutl.put("STR34", result_data.get("peer_acc_nm"));//目标账户名	100
						resutl.put("STR35", result_data.get("peer_acc_id"));//目标账户号	40
						resutl.put("ACC_BANK", "");//目标行名称	100
						resutl.put("REAMRK", result_data.get("aptn_tx"));//备注	100
						resutl.put("TX_STATE_CODE", result_data.get("tran_fg"));//指令状态	0-收到指令，待支付，1-已支付，2-未支付，3-退款	1
						resutl.put("STR36", result_data.get("actxt_tx"));//指令处理描述	100
						resutl.put("TX_TIME", result_data.get("last_update_time"));//退款时间	8
						resultList.add(resutl);	
						String str6= "{\"PRT_VCHR_INFO\": {"
								+	" \"ITEM_PRTNAME\": \"fmt\","//打印类型 fmt:eclispe画的,excell:表格通过画excell
								+	" \"DETAIL_INFO\":[{\"FILLER1\": {"//单域
													+	" \"RCV_TIME\": \""+ result_data.get("tran_time") +"\", "
													+	" \"CLI_SERIAL_NO\": \""+ (String) DataPoolUtil.getData(requestMessage, "CLI_SERIAL_NO") +"\", "
													+	" \"PAY_ID\": \""+ result_data.get("tran_sq") +"\", "
													+	" \"ACCT_NAME\": \""+ result_data.get("acct_nm") +"\", "
													+	" \"ACCOUNT\": "+ result_data.get("cpab_acc_id") +", "
													+	" \"OPEN_UNIT_CODE\": \""+ result_data.get("open_brh_id") +"\", "
													+	" \"STR33\": \""+ result_data.get("peer_brh_nm") +"\", "
													+	" \"STR35\": \""+ result_data.get("peer_acc_id") +"\", "
													+	" \"STR34\": \""+ result_data.get("peer_acc_nm") +"\", "
													+	" \"SETTL_AMT\": "+ new BigDecimal(result_data.get("tran_at").toString()) +", "
													+	" \"TX_STATE_CODE\": \""+ result_data.get("tranfg") +"\", "
													+	" \"OUT_INST_NO\": \""+ (String) DataPoolUtil.getData(requestMessage, "INST_NO") +"\", "
													+	" \"OPERATOR\": \""+ (String) DataPoolUtil.getData(requestMessage, "OPERATOR") +"\" "
												+	" },"
											+	" \"STR48\": \"\", "//打印时弹框提示语 为空就是默认
											+	" \"STR49\": \"0\", "//电子印章纵坐标 为空就是默认
											+	" \"STR50\": \"0\", "//电子印章横坐标 为空就是默认
											+	" \"VCHR_NO\": \"xmspfpayqry.fmt\" }]}}";				
						log.info("str6----->"+str6);
				
						DataPoolUtil.addData(requestMessage, "STR6", str6);
						DataPoolUtil.addData(requestMessage, "SND_RANG", "0");
						DataPoolUtil.addData(requestMessage, "STR7", new String(JSON.toJSONBytes(resultList),"UTF-8"));
						DataPoolUtil.addData(requestMessage, "RESPCODE", "1"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "交易成功");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "交易成功");
						return "0";						
					}else{
						log.info("查询无支付指令数据或支付指令待复核中");
						DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "查询无支付指令数据或支付指令待复核中");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "查询无支付指令数据或支付指令待复核中");
						return "0";
					}
					
				}else{
					HashMap<String,Object> hmp = new HashMap<String,Object>();
					hmp.put("merch_id",merch_id);
					hmp.put("ope_cd",ope_cd);
					hmp.put("orgcode",orgcode);
					hmp.put("busi_type",DataPoolUtil.getData(requestMessage, "TRAN_TYPE"));//指令类型	TRAN_TYPE	支付指令-0;其他-1
					hmp.put("busi_state",DataPoolUtil.getData(requestMessage, "FLAG2"));//指令状态	FLAG2	全部-0;待支付-1;已支付-2;退款-3;支付待复核-4;退款待复核-5
					hmp.put("tran_sq",DataPoolUtil.getData(requestMessage, "PAY_ID")==null ? "":DataPoolUtil.getData(requestMessage, "PAY_ID"));
					hmp.put("bgn_date",DataPoolUtil.getData(requestMessage, "BGN_DATE")==null ? "":DataPoolUtil.getData(requestMessage, "BGN_DATE"));
					hmp.put("end_date",DataPoolUtil.getData(requestMessage, "END_DATE")==null ? "":DataPoolUtil.getData(requestMessage, "END_DATE"));
					log.info("hmp="+hmp);
					Object[] result_datas = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.select_zjjg_tran_stat", hmp);
					log.info("length="+result_datas.length);
					if (result_datas.length > 0){
						JSONArray array = new JSONArray();
						Map<String,Object> result_data = null;
						for(int i =0; i < result_datas.length; i++){
							result_data = (Map<String, Object>) result_datas[i];
							Map<String, Object> map_add= new HashMap<String, Object>();
							map_add.put("BILL_NO", result_data.get("tran_sq"));//指令流水号
							map_add.put("TIMESTAMP", result_data.get("tran_time"));//指令接收时间
							map_add.put("ACCOUNT", result_data.get("cpab_acc_id"));//账户号
							map_add.put("CUSTNAME", result_data.get("acct_nm"));//账户名
							map_add.put("PROC_STAT", result_data.get("tranfg"));//指令状态
							map_add.put("STR50", result_data.get("tlr_id"));//操作员
							map_add.put("STR49", result_data.get("checker_id"));//复核员
							log.error("写入array的数据"+map_add);
							array.add(map_add);
						}
						String str2 = array.toJSONString();
						log.error("写入array的数据"+str7);				
						DataPoolUtil.addData(requestMessage, "STR2", str2);
						DataPoolUtil.addData(requestMessage, "RESPCODE", "1"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "交易成功");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "交易成功");
						return "0";
					}else{
						log.info("查询无支付指令数据");
						DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "查询无支付指令数据");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "查询无支付指令数据");
						return "0";
					}
				}				
			} else if ("02".equals(qry_type)) {
				log.info("xmspf-02-支付指令状态维护-申请");
				if ("02".equals(class_id)) {
					log.info("xmspf-0202-支付指令状态维护-申请");
					tran_type = "支付指令状态维护-申请";
					String pay_id=DataPoolUtil.getData(requestMessage, "PAY_ID")==null ? "":DataPoolUtil.getData(requestMessage, "PAY_ID").toString();
					if (pay_id.length()==0) {
						log.info("支付指令不能为空");
						DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令不能为空");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令不能为空");
						return "0";
					}
					String proc_stat=DataPoolUtil.getData(requestMessage, "PROC_STAT")==null ? "":DataPoolUtil.getData(requestMessage, "PROC_STAT").toString();
					if(proc_stat.length()==0){
						log.info("更新指令状态不能为空");
						DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "更新指令状态不能为空");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "更新指令状态不能为空");
						return "0";
					}
					//当指令状态选择2-未支付，3-退款时需要录入指令处理描述
					String str36=DataPoolUtil.getData(requestMessage, "STR36")==null ? "":DataPoolUtil.getData(requestMessage, "STR36").toString();;
					if (proc_stat.equals("3")) {
						if(str36.length()==0){
							log.info("指令状态更新为未支付时指令处理描述不能为空");
							DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "指令状态更新为未支付时指令处理描述不能为空");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "指令状态更新为未支付时指令处理描述不能为空");
							return "0";
						}
					}
					//当指令状态选择3-退款时需要录入退款时间
					String tx_time="";
					if(proc_stat.equals("3")){
						tx_time=DataPoolUtil.getData(requestMessage, "TX_TIME")==null ? "":DataPoolUtil.getData(requestMessage, "TX_TIME").toString();
						if(tx_time.length()==0){
							log.info("指令状态更新为退款时退款时间不能为空");
							DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "指令状态更新为退款时退款时间不能为空");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "指令状态更新为退款时退款时间不能为空");
							return "0";
						}
					}
					HashMap<String,Object> hmp = new HashMap<String,Object>();
					hmp.put("merch_id",merch_id);
					hmp.put("ope_cd",ope_cd);
					hmp.put("tran_sq",pay_id);
					log.info("hmp="+hmp);
					Object[] result_datas = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.select_zjjg_tran_detail", hmp);
					log.info("length="+result_datas.length);
					if (result_datas.length > 0){
						Map<String,Object> result_data = (Map<String, Object>) result_datas[0];
						String tran_fg=result_data.get("tran_fg").toString();//指令状态	0-收到指令，待支付，1-已支付，2-未支付（弃用），3-退款	1
						if (tran_fg.equals("0")) {//待支付状态
							if (proc_stat.equals("0")) {
								log.info("支付指令状态维护失败，原支付指令状态为待支付");
								DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
								DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，原支付指令状态为待支付");
								DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，原支付指令状态为待支付");
								return "0";
							} else if (proc_stat.equals("1")){//待支付状态只能改成已支付
								hmp.put("oth_msg1_tx",proc_stat);
								hmp.put("actxt_tx",str36);
								hmp.put("clear_time","0");
								hmp.put("tlr_id",(String) DataPoolUtil.getData(requestMessage,"OPERATOR"));
								log.info("hmp="+hmp);
								DatabaseExt.executeNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.update_zjjg_tran_detail", hmp);
								DataPoolUtil.addData(requestMessage, "RESPCODE", "1"); 
								DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护已提交复核员复核");
								DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护已提交复核员复核");
								return "0";
							} else if (proc_stat.equals("3")){//待支付状态改成退款请进行退款通知
								log.info("支付指令状态维护失败，待支付状态改成退款状态请进行退款通知");
								DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
								DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，待支付状态改成退款状态请进行退款通知");
								DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，待支付状态改成退款状态请进行退款通知");
								return "0";
							} else {
								log.info("支付指令状态维护失败，未知的指令状态");
								DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
								DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，未知的指令状态");
								DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，未知的指令状态");
								return "0";
							}
						} else if (tran_fg.equals("1")){//已支付
							if(proc_stat.equals("1")){
								log.info("支付指令状态维护失败，原支付指令状态为已支付");
								DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
								DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，原支付指令状态为已支付");
								DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，原支付指令状态为已支付");
								return "0";
							} else if (proc_stat.equals("3")){//已支付状态改成退款请进行退款通知
								log.info("支付指令状态维护失败，已支付状态改成退款状态请进行退款通知");
								DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
								DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，已支付状态改成退款状态请进行退款通知");
								DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，已支付状态改成退款请状态进行退款通知");
								return "0";
							} else {
								log.info("支付指令状态维护失败，未知的指令状态");
								DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
								DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，未知的指令状态");
								DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，未知的指令状态");
								return "0";
							}
						} else if (tran_fg.equals("3")){//已退款
							log.info("支付指令状态维护失败，原支付指令已退款");
							DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，原支付指令已退款");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，原支付指令已退款");
							return "0";
						} else {
							log.info("原支付指令状态异常，请联系管理员");
							DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "原支付指令状态异常，请联系管理员");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "原支付指令状态异常，请联系管理员");
							return "0";
						}
					} else {
						log.info("未查询到该支付指令，请重新查询");
						DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "未查询到该支付指令，请重新查询");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "未查询到该支付指令，请重新查询");
						return "0";
					}
				} else {
					tran_type = "支付指令状态维护查询-查询";
					String pay_id=DataPoolUtil.getData(requestMessage, "PAY_ID")==null ? "":DataPoolUtil.getData(requestMessage, "PAY_ID").toString();
					if(pay_id.length()==0){
						log.info("支付指令不能为空");
						DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令不能为空");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令不能为空");
						return "0";
					}
					List<HashMap<String, Object>> resultList = new ArrayList<HashMap<String,Object>>();	
					HashMap<String,Object> hmp = new HashMap<String,Object>();
					hmp.put("merch_id",merch_id);
					hmp.put("ope_cd",ope_cd);
					hmp.put("orgcode",orgcode);
					hmp.put("tran_sq",pay_id);
					log.info("hmp="+hmp);
					Object[] result_datas = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.select_zjjg_tran_detail", hmp);
					log.info("length="+result_datas.length);
					if (result_datas.length > 0){
						Map<String,Object> result_data = (Map<String, Object>) result_datas[0];
						HashMap<String,Object> resutl = new HashMap<String, Object>();
						resutl.put("RCV_TIME",result_data.get("tran_time"));//时间	20
						resutl.put("PAY_ID",result_data.get("tran_sq"));//指令流水号	30
						resutl.put("INST_NO",orgcode);//中平网点机构代码	20
						resutl.put("INST_NAME","");//中平网点机构名称	100
						resutl.put("OPEN_UNIT_CODE", result_data.get("open_brh_id"));//开户行代码	50
						resutl.put("UNIT_NAME", result_data.get("open_brh_nm"));//开户网点名称	100
						resutl.put("ACCT_NAME",result_data.get("acct_nm"));//账户名	100
						resutl.put("ACCOUNT", result_data.get("cpab_acc_id"));//账户号	40
						resutl.put("BUSI_TYPE",result_data.get("vch_type"));//支付类型	1：同行 2：跨行	1
						resutl.put("SETTL_AMT",result_data.get("tran_at"));//结算金额	
						resutl.put("ISS_ORG_NO", result_data.get("peer_brh_id"));//目标账户开户行代码	50
						resutl.put("STR33", result_data.get("peer_brh_nm"));//目标账户开户网点名称	100
						resutl.put("STR34", result_data.get("peer_acc_nm"));//目标账户名	100
						resutl.put("STR35", result_data.get("peer_acc_id"));//目标账户号	40
						resutl.put("ACC_BANK", "");//目标行名称	100
						resutl.put("REAMRK", result_data.get("aptn_tx"));//备注	100
						resutl.put("TX_STATE_CODE", result_data.get("tranfg"));//指令状态	0-收到指令，待支付，1-已支付，2-未支付，3-退款	
						
						resultList.add(resutl);	
						
						DataPoolUtil.addData(requestMessage, "STR7", new String(JSON.toJSONBytes(resultList),"UTF-8"));
						DataPoolUtil.addData(requestMessage, "RESPCODE", "1"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "交易成功");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "交易成功");
						return "0";
					}else{
						log.info("查询无支付指令数据或支付指令待复核中");
						DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "查询无支付指令数据或支付指令待复核中");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "查询无支付指令数据或支付指令待复核中");
						return "0";
					}
				}
				
			}else if("03".equals(qry_type)){
				tran_type = "支付指令退款通知-申请";
				log.info("xmspf-03-退款通知-申请");
				if("02".equals(class_id)){
					log.info("xmspf-0302-退款通知");
					String pay_id=DataPoolUtil.getData(requestMessage, "PAY_ID")==null ? "":DataPoolUtil.getData(requestMessage, "PAY_ID").toString();
					if(pay_id.length()==0){
						log.info("支付指令不能为空");
						DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令不能为空");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令不能为空");
						return "0";
					}
					String tx_time=DataPoolUtil.getData(requestMessage, "TX_TIME")==null ? "":DataPoolUtil.getData(requestMessage, "TX_TIME").toString();
					if(tx_time.length() < 14){
						log.info("退款时间为空或者长度不合法，重置退款时间为当前时间");
						tx_time =  new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
					}
					String str36=DataPoolUtil.getData(requestMessage, "STR36")==null ? "":DataPoolUtil.getData(requestMessage, "STR36").toString();
					if(str36.length()==0){
						log.info("指令状态更新为未支付/退款时指令处理描述不能为空");
						DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "指令状态更新为未支付/退款时指令处理描述不能为空");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "指令状态更新为未支付/退款时指令处理描述不能为空");
						return "0";
					}
					HashMap<String,Object> hmp = new HashMap<String,Object>();
					hmp.put("merch_id",merch_id);
					hmp.put("ope_cd",ope_cd);
					hmp.put("tran_sq",pay_id);
					log.info("hmp="+hmp);
					Object[] result_datas = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.select_zjjg_tran_detail", hmp);
					log.info("length="+result_datas.length);
					if (result_datas.length > 0){
						Map<String,Object> result_data = (Map<String, Object>) result_datas[0];
						String tran_fg=result_data.get("tran_fg").toString();//指令状态	0-收到指令，待支付，1-已支付，2-未支付，3-退款	1
						if (tran_fg.equals("0") || tran_fg.equals("1")) {// 待支付与已支付可修改为退款
							log.info("退款已提交复核员复核");
							HashMap<String,Object> parmaMap = new HashMap<String,Object>();
							parmaMap.put("merch_id",merch_id);
							parmaMap.put("ope_cd",ope_cd);
							parmaMap.put("tran_sq",pay_id);
							parmaMap.put("actxt_tx",str36);//退款描述
							parmaMap.put("last_update_time",tx_time);//退款时间
							parmaMap.put("oth_msg1_tx", "3");//待复核状态
							parmaMap.put("tlr_id", (String) DataPoolUtil.getData(requestMessage,"OPERATOR"));//操作员
							log.info("parmaMap="+parmaMap);
							DatabaseExt.executeNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.update_zjjg_tran_detail", parmaMap);
							DataPoolUtil.addData(requestMessage, "RESPCODE", "1"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "退款通知已提交复核员复核");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "退款通知已提交复核员复核");
							return "0";
						} else if (tran_fg.equals("3")) {
							log.info("支付指令状态维护失败，原支付指令状态为退款");
							DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，原支付指令状态为退款");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，原支付指令状态为退款");
							return "0";
						} else {
							log.info("原支付指令状态不支持发起退款");
							DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "原支付指令状态不支持发起退款");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "原支付指令状态不支持发起退款");
							return "0";
						}
					} else {
						log.info("查询无支付指令数据或支付指令待复核中");
						DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "查询无支付指令数据或支付指令待复核中");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "查询无支付指令数据或支付指令待复核中");
						return "0";
					}
				}else{
					tran_type = "支付指令退款查询-申请";
					log.info("xmspf-03-支付指令退款查询-申请");
					String pay_id=DataPoolUtil.getData(requestMessage, "PAY_ID")==null ? "":DataPoolUtil.getData(requestMessage, "PAY_ID").toString();
					if(pay_id.length()==0){
						log.info("支付指令不能为空");
						DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令不能为空");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令不能为空");
						return "0";
					}
					List<HashMap<String, Object>> resultList = new ArrayList<HashMap<String,Object>>();	
					HashMap<String,Object> hmp = new HashMap<String,Object>();
					hmp.put("merch_id",merch_id);
					hmp.put("ope_cd",ope_cd);
					hmp.put("orgcode",orgcode);
					hmp.put("tran_sq",pay_id);
					log.info("hmp="+hmp);
					Object[] result_datas = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.select_zjjg_tran_detail", hmp);
					log.info("length="+result_datas.length);
					if (result_datas.length > 0){
						Map<String,Object> result_data = (Map<String, Object>) result_datas[0];
						HashMap<String,Object> resutl = new HashMap<String, Object>();
						resutl.put("RCV_TIME",result_data.get("tran_time"));//时间	20
						resutl.put("PAY_ID",result_data.get("tran_sq"));//指令流水号	30
						resutl.put("INST_NO",orgcode);//中平网点机构代码	20
						resutl.put("INST_NAME","");//中平网点机构名称	100
						resutl.put("OPEN_UNIT_CODE", result_data.get("open_brh_id"));//开户行代码	50
						resutl.put("UNIT_NAME", result_data.get("open_brh_nm"));//开户网点名称	100
						resutl.put("ACCT_NAME",result_data.get("acct_nm"));//账户名	100
						resutl.put("ACCOUNT", result_data.get("cpab_acc_id"));//账户号	40
						resutl.put("BUSI_TYPE",result_data.get("vch_type"));//支付类型	1：同行 2：跨行	1
						resutl.put("SETTL_AMT",result_data.get("tran_at"));//结算金额	
						resutl.put("ISS_ORG_NO", result_data.get("peer_brh_id"));//目标账户开户行代码	50
						resutl.put("STR33", result_data.get("peer_brh_nm"));//目标账户开户网点名称	100
						resutl.put("STR34", result_data.get("peer_acc_nm"));//目标账户名	100
						resutl.put("STR35", result_data.get("peer_acc_id"));//目标账户号	40
						resutl.put("ACC_BANK", "");//目标行名称	100
						resutl.put("REAMRK", result_data.get("aptn_tx"));//备注	100
						resutl.put("TX_STATE_CODE", result_data.get("tranfg"));//指令状态	0-收到指令，待支付，1-已支付，2-未支付，3-退款	1
						
						resultList.add(resutl);	
						
						DataPoolUtil.addData(requestMessage, "STR7", new String(JSON.toJSONBytes(resultList),"UTF-8"));
						DataPoolUtil.addData(requestMessage, "RESPCODE", "1"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "交易成功");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "交易成功");
						return "0";
					}else{
						log.info("查询无支付指令数据");
						DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
						DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "查询无支付指令数据或待复核中");
						DataPoolUtil.addData(requestMessage, "RESPINFO", "查询无支付指令数据");
						return "0";
					}
				}
			} else if("04".equals(qry_type)){
				if ("01".equals(class_code)) {//支付指令状态维护
					if ("02".equals(class_id)) {
						tran_type = "支付指令状态维护-复核";
						log.info("xmspf-04-01-支付指令状态维护-复核");
						String pay_id=DataPoolUtil.getData(requestMessage, "PAY_ID")==null ? "":DataPoolUtil.getData(requestMessage, "PAY_ID").toString();
						if(pay_id.length()==0){
							log.info("支付指令不能为空");
							DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令不能为空");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令不能为空");
							return "0";
						}
						HashMap<String,Object> hmp = new HashMap<String,Object>();
						hmp.put("merch_id",merch_id);
						hmp.put("ope_cd",ope_cd);
						hmp.put("tran_sq",pay_id);
						log.info("hmp="+hmp);
						Object[] result_datas = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.select_zjjg_tran_detail_review", hmp);
						log.info("length="+result_datas.length);
						if (result_datas.length > 0){
							Map<String,Object> result_data = (Map<String, Object>) result_datas[0];
							String tlr_id=result_data.get("tlr_id").toString();//操作员
							String checker_id=(String) DataPoolUtil.getData(requestMessage,"OPERATOR");//复核员
							if (checker_id.equals(tlr_id)) {
								log.info("复核柜员不能与操作柜员是同一人，请更换复核柜员");
								DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
								DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "复核柜员不能与操作柜员是同一人，请更换复核柜员");
								DataPoolUtil.addData(requestMessage, "RESPINFO", "复核柜员不能与操作柜员是同一人，请更换复核柜员");
								return "0";
							}
							String flag1 = (String) DataPoolUtil.getData(requestMessage, "FLAG1");// 审核结果
							if ("1".equals(flag1)) {
								String proc_stat=result_data.get("oth_msg1_tx").toString();//待复核状态;
								log.info("proc_stat=" + proc_stat);
								if(proc_stat.length()==0){
									log.info("当前指令待复核状态为空，请重新查询");
									DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
									DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "当前指令待复核状态为空，请重新查询");
									DataPoolUtil.addData(requestMessage, "RESPINFO", "当前指令待复核状态为空，请重新查询");
									return "0";
								}
								if ("待支付".equals(proc_stat)) {
									proc_stat = "0";
								} else if ("已支付".equals(proc_stat)){
									proc_stat = "1";
								} else if ("退款".equals(proc_stat)){
									proc_stat = "3";
								}
								String tran_fg=result_data.get("tran_fg").toString();//指令状态	0-收到指令，待支付，1-已支付，2-未支付，3-退款	1
								if (tran_fg.equals("0")) {//待支付状态
									if (proc_stat.equals("0")) {
										log.info("支付指令状态维护失败，原支付指令状态为待支付");
										DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
										DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，原支付指令状态为待支付");
										DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，原支付指令状态为待支付");
										return "0";
									} else if (proc_stat.equals("1")) {
										log.info("原支付指令状态待支付可改成已支付状态");
										hmp.put("tran_fg",proc_stat);
										hmp.put("oth_msg1_tx", "");
										hmp.put("oth_msg2_tx", "1");
										hmp.put("checker_id", checker_id);
										hmp.put("clear_time","0");
										log.info("hmp="+hmp);
										DatabaseExt.executeNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.update_zjjg_tran_detail", hmp);
										DataPoolUtil.addData(requestMessage, "RESPCODE", "1"); 
										DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "交易成功");
										DataPoolUtil.addData(requestMessage, "RESPINFO", "交易成功");
										return "0";
									} else if (proc_stat.equals("3")){
										log.info("支付指令状态维护失败，待支付状态改成退款状态请进行退款通知");
										DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
										DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，待支付状态改成退款状态请进行退款通知");
										DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，待支付状态改成退款状态请进行退款通知");
										return "0";
									} else {
										log.info("支付指令状态维护失败，未知的指令状态");
										DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
										DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，未知的指令状态");
										DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，未知的指令状态");
										return "0";
									}
								} else if(tran_fg.equals("1")) {//已支付
									if(proc_stat.equals("1")){
										log.info("支付指令状态维护失败，原支付指令状态为已支付");
										DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
										DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，原支付指令状态为已支付");
										DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，原支付指令状态为已支付");
										return "0";
									} else if (proc_stat.equals("3")){
										log.info("支付指令状态维护失败，已支付状态改成退款状态请进行退款通知");
										DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
										DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，已支付状态改成退款状态请进行退款通知");
										DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，已支付状态改成退款请状态进行退款通知");
										return "0";
									} else {
										log.info("支付指令状态维护失败，未知的指令状态");
										DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
										DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，未知的指令状态");
										DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，未知的指令状态");
										return "0";
									}
								}else if(tran_fg.equals("3")){//已退款
									log.info("支付指令状态维护失败，原支付指令已退款");
									DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
									DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，原支付指令已退款");
									DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，原支付指令已退款");
									return "0";
								} else {
									log.info("原支付指令状态异常，请联系管理员");
									DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
									DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "原支付指令状态异常，请联系管理员");
									DataPoolUtil.addData(requestMessage, "RESPINFO", "原支付指令状态异常，请联系管理员");
									return "0";
								}
							} else {
								log.info("交易审核不通过，清除交易待复核状态");
								hmp.put("actxt_tx", "");
								hmp.put("oth_msg1_tx", "");
								hmp.put("oth_msg2_tx", "2");
								hmp.put("clear_time", "1");
								hmp.put("checker_id", (String) DataPoolUtil.getData(requestMessage,"OPERATOR"));
								DatabaseExt.executeNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.update_zjjg_tran_detail", hmp);
								DataPoolUtil.addData(requestMessage, "RESPCODE", "1"); 
								DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "交易成功，交易审核修改为不通过");
								DataPoolUtil.addData(requestMessage, "RESPINFO", "交易成功，交易审核修改为不通过");
								return "0";
							}
						} else {
							log.info("查询无待复核支付指令数据");
							DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "查询无待复核支付指令退款通知数据");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "查询无待复核支付指令退款通知数据");
							return "0";
						}
					} else {
						tran_type = "支付指令状态维护查询-复核";
						log.info("xmspf-04-01-支付指令状态维护查询-复核");
						List<HashMap<String, Object>> resultList = new ArrayList<HashMap<String,Object>>();	
						HashMap<String,Object> hmp = new HashMap<String,Object>();
						hmp.put("merch_id",merch_id);
						hmp.put("ope_cd", ope_cd);
						hmp.put("orgcode", orgcode);
						hmp.put("oth_msg1_tx", "1");
						log.info("hmp="+hmp);
						Object[] result_datas = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.select_zjjg_tran_detail_review", hmp);
						log.info("length="+result_datas.length);
						if (result_datas.length > 0){
							for(int i =0; i < result_datas.length; i++){
								Map<String,Object> result_data = (Map<String, Object>) result_datas[i];
								HashMap<String,Object> resutl = new HashMap<String, Object>();
								resutl.put("RCV_TIME",result_data.get("tran_time"));//时间	20
								resutl.put("PAY_ID",result_data.get("tran_sq"));//指令流水号	30
								resutl.put("TX_STATE_CODE", result_data.get("tranfg"));//指令状态	0-收到指令，待支付，1-已支付，2-未支付，3-退款	1
								resutl.put("STR49", result_data.get("oth_msg1_tx"));//待复核状态
								resutl.put("REAMRK", result_data.get("aptn_tx"));//备注	100
								resutl.put("STR50",result_data.get("tlr_id"));//操作员	30
								resultList.add(resutl);	
							}
							DataPoolUtil.addData(requestMessage, "STR2", new String(JSON.toJSONBytes(resultList),"UTF-8"));
							resultList.clear();
							HashMap<String,Object> resutl = new HashMap<String, Object>();
							resutl.put("CLASS_CODE", class_code);
							resultList.add(resutl);
							DataPoolUtil.addData(requestMessage, "STR7", new String(JSON.toJSONBytes(resultList),"UTF-8"));
							DataPoolUtil.addData(requestMessage, "RESPCODE", "1"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "交易成功");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "交易成功");
							return "0";
						}else{
							log.info("查询无待复核支付指令数据");
							DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "查询无待复核支付指令状态维护数据");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "查询无待复核支付指令状态维护数据");
							return "0";
						}
					}
				} else if ("02".equals(class_code)){//退款通知
					if ("02".equals(class_id)) {
						tran_type = "支付指令退款通知-复核";
						log.info("xmspf-04-02-支付指令退款通知-复核");
						String pay_id=DataPoolUtil.getData(requestMessage, "PAY_ID")==null ? "":DataPoolUtil.getData(requestMessage, "PAY_ID").toString();
						if(pay_id.length()==0){
							log.info("支付指令不能为空");
							DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令不能为空");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令不能为空");
							return "0";
						}
						HashMap<String,Object> hmp = new HashMap<String,Object>();
						hmp.put("merch_id",merch_id);
						hmp.put("ope_cd",ope_cd);
						hmp.put("tran_sq",pay_id);
						hmp.put("oth_msg1_tx", "3");
						log.info("hmp="+hmp);
						Object[] result_datas = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.select_zjjg_tran_detail_review", hmp);
						log.info("length="+result_datas.length);
						if (result_datas.length > 0){
							Map<String,Object> result_data = (Map<String, Object>) result_datas[0];
							String tlr_id=result_data.get("tlr_id").toString();//操作员
							String checker_id=(String) DataPoolUtil.getData(requestMessage,"OPERATOR");//复核员
							if (checker_id.equals(tlr_id)) {
								log.info("复核柜员不能与操作柜员是同一人，请更换复核柜员");
								DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
								DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "复核柜员不能与操作柜员是同一人，请更换复核柜员");
								DataPoolUtil.addData(requestMessage, "RESPINFO", "复核柜员不能与操作柜员是同一人，请更换复核柜员");
								return "0";
							}
							String flag1 = (String) DataPoolUtil.getData(requestMessage, "FLAG1");// 审核结果
							if ("1".equals(flag1)) {
								String tran_fg=result_data.get("tran_fg").toString();//指令状态	0-收到指令，待支付，1-已支付，2-未支付，3-退款	
								if (tran_fg.equals("0") || tran_fg.equals("1")) {
									String tx_time=result_data.get("last_update_time").toString();//退款时间
									if(tx_time.length()==0){
										log.info("原指令退款时间为空，请重新查询");
										DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
										DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "原指令退款时间为空，请重新查询");
										DataPoolUtil.addData(requestMessage, "RESPINFO", "原指令退款时间为空，请重新查询");
										return "0";
									}
									//报文头赋值
									DataPoolUtil.addData(requestMessage, "TRAN_TYPE", "20004"); 
									DataPoolUtil.addData(requestMessage, "OPR_NAME", "ycyh"); 
									DataPoolUtil.addData(requestMessage, "PASSWORD", "xmyc2016"); 
									DataPoolUtil.addData(requestMessage, "TLR_NAME", ""); 
									DataPoolUtil.addData(requestMessage, "STR31", ""); 
									//接口类型赋值
									DataPoolUtil.addData(requestMessage, "INTER_CODE", "xmspf_20004"); 
									//body参数赋值
									DataPoolUtil.addData(requestMessage, "PAY_ID", pay_id); 
									DataPoolUtil.addData(requestMessage, "STR45", pay_id); 
									DataPoolUtil.addData(requestMessage, "TX_DATE",tx_time.substring(0, 4) + "-" + tx_time.substring(4, 6)+ "-" + tx_time.substring(6, 8) +" "+tx_time.substring(8, 10)+":"+tx_time.substring(10, 12)+":"+tx_time.substring(12, 14) ); 
									return "1";
								} else if (tran_fg.equals("3")) {
									log.info("支付指令状态维护失败，原支付指令状态为退款");
									DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
									DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "支付指令状态维护失败，原支付指令状态为退款");
									DataPoolUtil.addData(requestMessage, "RESPINFO", "支付指令状态维护失败，原支付指令状态为退款");
									return "0";
								} else {
									log.info("原支付指令状态不支持发起退款");
									DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
									DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "原支付指令状态不支持发起退款");
									DataPoolUtil.addData(requestMessage, "RESPINFO", "原支付指令状态不支持发起退款");
									return "0";
								}
							} else {
								log.info("交易审核不通过，清除交易待复核状态");
								hmp.put("actxt_tx", "");
								hmp.put("oth_msg1_tx", "");
								hmp.put("oth_msg2_tx", "2");
								hmp.put("clear_time", "1");
								hmp.put("checker_id", (String) DataPoolUtil.getData(requestMessage,"OPERATOR"));
								DatabaseExt.executeNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.update_zjjg_tran_detail", hmp);
								DataPoolUtil.addData(requestMessage, "RESPCODE", "1"); 
								DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "交易成功，交易审核不通过");
								DataPoolUtil.addData(requestMessage, "RESPINFO", "交易成功，交易审核不通过");
								return "0";
							}
						}else{
							log.info("查询无支付指令数据");
							DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "查询无待复核支付指令退款通知数据");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "查询无待复核支付指令退款通知数据");
							return "0";
						}
					} else {
						tran_type = "支付指令退款通知查询-复核";
						log.info("xmspf-04-02-支付指令退款通知查询-复核");
						List<HashMap<String, Object>> resultList = new ArrayList<HashMap<String,Object>>();	
						HashMap<String,Object> hmp = new HashMap<String,Object>();
						hmp.put("merch_id",merch_id);
						hmp.put("ope_cd", ope_cd);
						hmp.put("orgcode", orgcode);
						hmp.put("oth_msg1_tx", "3");
						log.info("hmp="+hmp);
						Object[] result_datas = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.select_zjjg_tran_detail_review", hmp);
						log.info("length="+result_datas.length);
						if (result_datas.length > 0){
							for(int i =0; i < result_datas.length; i++){
								Map<String,Object> result_data = (Map<String, Object>) result_datas[i];
								HashMap<String,Object> resutl = new HashMap<String, Object>();
								resutl.put("RCV_TIME",result_data.get("tran_time"));//时间	20
								resutl.put("PAY_ID",result_data.get("tran_sq"));//指令流水号	30
								resutl.put("TX_STATE_CODE", result_data.get("tranfg"));//原指令状态	0-收到指令，待支付，1-已支付，2-未支付，3-退款	1
								resutl.put("STR49", result_data.get("oth_msg1_tx"));//待复核状态
								resutl.put("REAMRK", result_data.get("aptn_tx"));//备注	100
								resutl.put("STR50",result_data.get("tlr_id"));//操作员	30
								resultList.add(resutl);	
							}
							DataPoolUtil.addData(requestMessage, "STR2", new String(JSON.toJSONBytes(resultList),"UTF-8"));
							resultList.clear();
							HashMap<String,Object> resutl = new HashMap<String, Object>();
							resutl.put("CLASS_CODE", class_code);
							resultList.add(resutl);
							DataPoolUtil.addData(requestMessage, "STR7", new String(JSON.toJSONBytes(resultList),"UTF-8"));
							DataPoolUtil.addData(requestMessage, "RESPCODE", "1"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "交易成功");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "交易成功");
							return "0";
						}else{
							log.info("查询无待复核支付指令数据");
							DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
							DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "查询无待复核支付指令退款通知数据");
							DataPoolUtil.addData(requestMessage, "RESPINFO", "查询无待复核支付指令退款通知数据");
							return "0";
						}
					}
				} else {
					tran_type = "复核类型不存在";
					log.error("复核类型不存在");
					log.info("xmspf-复核类型不存在");
					DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
					DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "复核类型不存在");
					DataPoolUtil.addData(requestMessage, "RESPINFO", "复核类型不存在");
					return "0";
				}
			} else {
				tran_type = "交易类型不存在";
				log.info("xmspf-交易类型不存在");
				DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
				DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "交易类型不存在");
				DataPoolUtil.addData(requestMessage, "RESPINFO", "交易类型不存在");
				return "0";
			}
		} catch (Exception e) {
			log.error("["+message_id+"] PaydealPub异常，请核实出错定位：" +  PrintLog.getTrace(e));
			DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
			DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "PaydealPub处理异常");
			DataPoolUtil.addData(requestMessage, "RESPINFO", "PaydealPub处理异常");
			return "0";
		} finally {
			HashMap<String, String> paramMap = new HashMap<>();
			String out_sys_code = (String) DataPoolUtil.getData(requestMessage,"OUTSYS_CODE");
			String merch_id = (String) DataPoolUtil.getData(requestMessage,"MERCH_ID");
			String ope_cd = (String) DataPoolUtil.getData(requestMessage,"OPE_CD");
			Long tran_sq = (Long) DataPoolUtil.getData(requestMessage,"CEN_SERIAL_NO");
			String acc_serial_sq = (String) DataPoolUtil.getData(requestMessage,"CLI_SERIAL_NO");
			String tran_inst_id = (String) DataPoolUtil.getData(requestMessage,"INST_NO");
			String tran_cd = (String) DataPoolUtil.getData(requestMessage,"TX_CODE");
			String tran_dt = new SimpleDateFormat("yyyyMMdd").format(new Date());
			String tlr_id = (String) DataPoolUtil.getData(requestMessage,"OPERATOR");
			String pay_id = (String)DataPoolUtil.getData(requestMessage,"PAY_ID");
			String respInfo = (String) DataPoolUtil.getData(requestMessage,"RESPINFO");
			String respCode = (String)DataPoolUtil.getData(requestMessage,"RESPCODE");
			paramMap.put("out_sys_code", out_sys_code);
			paramMap.put("merch_id", merch_id);
			paramMap.put("ope_cd", ope_cd);
			paramMap.put("tran_sq", tran_sq.toString());
			paramMap.put("acc_serial_sq", acc_serial_sq);
			paramMap.put("tran_inst_id", tran_inst_id);
			paramMap.put("tran_cd", tran_cd);
			paramMap.put("tran_dt", tran_dt);
			paramMap.put("tlr_id", tlr_id);
			paramMap.put("pay_id", pay_id);
			paramMap.put("oth_msg1_tx", respCode + "|" + respInfo);
			paramMap.put("tran_stat_cd", respCode);
			paramMap.put("oth_msg2_tx", message_id);
			paramMap.put("oth_pr_msg3", tran_type);
			log.info(LogTableDAO.insertLogRecord(paramMap));
		}
	}
	
	@Bizlet("退款后处理")
	public static void PaydealRet(IKernelServiceContext context) {
		//获取容器
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		try {
			String message_id = (String) requestMessage.getHeaders().get("$btp.message.id");
			log.debug("["+message_id+"] For循环进行Map集合遍历--->");
			Map<String, Object> mapData = DataPoolUtil.getDataPool(requestMessage);
			for (Map.Entry<String, Object> m : mapData.entrySet()) {
				log.debug("["+message_id+"] " + m.getKey() + ";Value:" + m.getValue());
			}
			String merch_id = (String)DataPoolUtil.getData(requestMessage,"MERCH_ID");
			log.info("委托单位代码merch_id=" + merch_id);
			String ope_cd = (String)DataPoolUtil.getData(requestMessage,"OPE_CD");
			log.info("业务代码ope_cd=" + ope_cd);
			String REMARKS = String.valueOf(DataPoolUtil.getData(requestMessage, "REMARKS"));
			String[] orgInfo=REMARKS.split("\\|");
			DataPoolUtil.addData(requestMessage, "MESSAGE_MARK", orgInfo[0]);
			DataPoolUtil.addData(requestMessage, "DECODE_IP", orgInfo[1]);
			DataPoolUtil.addData(requestMessage, "INTER_CODE", orgInfo[2]);
			DataPoolUtil.addData(requestMessage, "SND_RANG", "0");
			DataPoolUtil.addData(requestMessage, "FUNC_CHOOSE", "0");
			String respcode = (String) DataPoolUtil.getData(requestMessage, "STATE");//委托方响应结果
			if(respcode.equals("1")){//委托方响应成功，需更新对应的退款状态
				//指令流水号
				//String pay_id=(String)DataPoolUtil.getData(requestMessage,"PAY_ID");
				String pay_id=(String)DataPoolUtil.getData(requestMessage,"STR45");
				//是否成功，0不成功，1成功
				String result_sta=(String)DataPoolUtil.getData(requestMessage,"RESULT_STA");
				if(result_sta.equals("1")){
					HashMap<String,Object> hmp = new HashMap<String,Object>();
					hmp.put("merch_id",merch_id);
					hmp.put("ope_cd",ope_cd);
					hmp.put("tran_sq",pay_id);
					hmp.put("tran_fg","3");//退款
					hmp.put("oth_msg1_tx", "");
					hmp.put("oth_msg2_tx", "1");
					hmp.put("clear_time", "0");
					hmp.put("checker_id", (String) DataPoolUtil.getData(requestMessage,"OPERATOR"));
					log.info("hmp="+hmp);
					DatabaseExt.executeNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.update_zjjg_tran_detail", hmp);
					DataPoolUtil.addData(requestMessage, "RESPCODE", "1"); 
				} else {
					DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
				}
			}else{
				DataPoolUtil.addData(requestMessage, "RESPCODE", "0"); 
				log.info("委托方返回响应信息：" + (String)DataPoolUtil.getData(requestMessage, "STR50"));
			} 
			DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", (String)DataPoolUtil.getData(requestMessage, "STR50"));
			DataPoolUtil.addData(requestMessage, "RESPINFO", (String)DataPoolUtil.getData(requestMessage, "STR50"));
		} catch (Exception e) {
			log.error("系统执行发生异常：" + PrintLog.getTrace(e));
		} finally {
			HashMap<String, String> paramMap = new HashMap<>();
			String merch_id = (String) DataPoolUtil.getData(requestMessage,"MERCH_ID");
			String ope_cd = (String) DataPoolUtil.getData(requestMessage,"OPE_CD");
			String acc_serial_sq = (String) DataPoolUtil.getData(requestMessage,"CLI_SERIAL_NO");
			String pay_id = (String)DataPoolUtil.getData(requestMessage,"PAY_ID");
			String respInfo = (String) DataPoolUtil.getData(requestMessage,"RESPINFO");
			String respCode = (String)DataPoolUtil.getData(requestMessage,"RESPCODE");
			paramMap.put("merch_id", merch_id);
			paramMap.put("ope_cd", ope_cd);
			paramMap.put("old_acc_serial_sq", acc_serial_sq);
			paramMap.put("old_pay_id", pay_id);
			paramMap.put("oth_msg1_tx", respCode + "|" + respInfo);
			paramMap.put("tran_stat_cd", respCode);
			log.info(LogTableDAO.updateLogRecord(paramMap));
		}
	}
	

	
	/**
	 * 通用调用后台，覆盖返回值
	 * @param context
	 * @return
	 */
	@Bizlet("通用调用后台")
	public static void CommonHost(IKernelServiceContext context) {
		//获取容器
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		log.debug("=========通用调用后台=========");
		DataPoolUtil.addData(requestMessage, "INTER_CODE", "OSTS001");
		HostServiceInvoker.invoke(context);
	}
	
	@Bizlet
	public void errorCheck(IKernelServiceContext context){
		//获取容器
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		DataPoolUtil.addData(requestMessage, "RET_CODE", "9999");
		DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "系统错误");
		DataPoolUtil.addData(requestMessage, "RESPINFO", "系统错误");
	}
}







