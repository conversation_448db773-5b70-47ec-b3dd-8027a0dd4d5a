package com.psbc.pfpj.prov3502;

import java.io.UnsupportedEncodingException;

import com.primeton.btp.api.core.exception.BTPRuntimeException;
import com.primeton.btp.api.core.logger.ILogger;
import com.primeton.btp.api.core.logger.LoggerFactory;
import com.primeton.btp.api.core.utils.ExtPropertyHelper;
import com.primeton.btp.api.tcp.netty.buffer.ChannelBuffer;
import com.primeton.btp.api.tcp.netty.buffer.ChannelBuffers;
import com.primeton.btp.api.tcp.netty.channel.Channel;
import com.primeton.btp.api.tcp.netty.channel.ChannelHandlerContext;
import com.primeton.btp.api.transport.tcp.ITcpTransport;
import com.primeton.btp.api.transport.tcp.exception.ExceptionCodes;
import com.primeton.btp.spi.transport.tcp.ITcpDataExchange;
import com.primeton.btp.spi.transport.tcp.AbstractTcpTransport;

public class Xmspf_dsCustomTcpDataExchange implements ITcpDataExchange {
	private ILogger logger = LoggerFactory.getLogger(Xmspf_dsCustomTcpDataExchange.class);
	private ITcpTransport transport;

	protected int headBytesLength = 4;

	protected boolean lengthContainsHeadBytes = true;

	public static final int MAX_HEAD_BYTES_LENGTH = 4;

	public static final int DEFAULT_HEAD_BYTES_LENGTH = 4;

	public void setTransport(ITcpTransport transport) {
		this.transport = transport;
		this.headBytesLength = ExtPropertyHelper.getEntryValueAsInt(transport.getDefinition().getExtProperty(),
				"HEAD_BYTES_LENGTH", 4);
		if (this.headBytesLength <= 0 || this.headBytesLength > 4)
			throw new BTPRuntimeException(ExceptionCodes.SPECIFIED_HEAD_BYTES_LENGTH_ERROR,
					new String[] { String.valueOf(this.headBytesLength), String.valueOf(4) });
	}

	public byte[] read(ChannelBuffer buffer) {
		return read(null, null, buffer, null);
	}

	public byte[] read(ChannelHandlerContext ctx, Channel channel, ChannelBuffer buffer,
			AbstractTcpTransport transport) {
		logger.info(buffer.readableBytes()+":"+this.headBytesLength);
		if (buffer.readableBytes() < 14) {
			logger.info("报文长度不满足最低长度");
			return null;
		}
		logger.info("数据转换-----read-----："+ buffer.readableBytes());
		byte[] receiveBytes = new byte[buffer.readableBytes()];
		buffer.readBytes(receiveBytes);
		return receiveBytes;
	}

	public ChannelBuffer write(byte[] bytes) {
		int dataLength = bytes.length;
		logger.info("数据转换-----write-----："+dataLength);
		ChannelBuffer sendBuffer = ChannelBuffers.buffer(dataLength);
		sendBuffer.writeBytes(bytes);
		return sendBuffer;
		/*int dataLength = bytes.length;
		int totalLength = this.headBytesLength + dataLength;
		ChannelBuffer sendBuffer = ChannelBuffers.buffer(totalLength);
		byte[] headBytes = new byte[this.headBytesLength];
		if (this.lengthContainsHeadBytes) {
			Bits.putInt(headBytes, totalLength);
		} else {
			Bits.putInt(headBytes, dataLength);
		}
		sendBuffer.writeBytes(headBytes);
		sendBuffer.writeBytes(bytes);
		return sendBuffer;*/
	}

	public ITcpTransport getTransport() {
		return this.transport;
	}
}
