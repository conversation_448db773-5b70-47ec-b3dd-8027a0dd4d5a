/*******************************************************************************
 * $Header: /cvsroot/PTP50/code/btp/develop/src/server/com.primeton.btp.endpoint.http/src/main/java/com/primeton/btp/impl/endpoint/http/HttpResponseFutureCallback.java,v 1.2 2013/09/06 08:13:00 wuyh Exp $
 * $Revision: 1.2 $
 * $Date: 2013/09/06 08:13:00 $
 *
 *==============================================================================
 *
 * Copyright (c) 2001-2006 Primeton Technologies, Ltd.
 * All rights reserved.
 *
 * Created on 2011-3-29
 *******************************************************************************/


package com.psbc.pfpj.prov3502.core;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import com.primeton.btp.api.core.exception.BTPRuntimeException;
import com.primeton.btp.api.core.logger.ILogger;
import com.primeton.btp.api.core.logger.LoggerFactory;
import com.primeton.btp.api.core.utils.ByteArrayUtils;
import com.primeton.btp.api.endpoint.EndpointResponse;
import com.primeton.btp.api.endpoint.IEndpointResponseCallback;
import com.primeton.btp.api.endpoint.http.IHttpEndpointDefinition;
import com.primeton.btp.api.engine.hosttrans.IHostTransServiceDefinition;
import com.primeton.btp.api.message.DataPoolUtil;
import com.primeton.btp.impl.endpoint.http.HttpResponseFutureCallback;
import com.primeton.btp.impl.endpoint.http.NUrlEncodedFormEntity;
import com.primeton.btp.impl.endpoint.log.EndpointSystemLogger;
import com.primeton.btp.spi.endpoint.http.AbstractHttpEndpoint;
import com.primeton.btp.spi.endpoint.http.AbstractHttpEndpointCaller;
import com.primeton.btp.spi.endpoint.http.AbstractHttpEndpointResponseCreator;
import com.primeton.components.api.httpclient.apache.HttpResponse;
import com.primeton.components.api.httpclient.apache.NameValuePair;
import com.primeton.components.api.httpclient.apache.client.methods.HttpPost;
import com.primeton.components.api.httpclient.apache.entity.AbstractHttpEntity;
import com.primeton.components.api.httpclient.apache.message.BasicNameValuePair;
import com.primeton.components.api.httpclient.apache.nio.client.HttpAsyncClient;
import com.primeton.components.api.httpclient.apache.nio.concurrent.FutureCallback;
import com.primeton.components.api.httpclient.apache.nio.entity.NByteArrayEntity;
import com.primeton.components.api.httpclient.apache.nio.entity.NStringEntity;
import com.primeton.tip.org.springframework.integration.core.Message;

/**
 * TODO 此处填写 class 信息
 *
 * <AUTHOR> (mailto:<EMAIL>)
 */
public class MyHttpRFDoubleCallback implements FutureCallback<HttpResponse> {
	private static ILogger logger = LoggerFactory.getLogger(MyHttpRFDoubleCallback.class);
	private String endpointResponseCallbackId;
	private AbstractHttpEndpoint endpoint;
	private String requestUrl;
	private AbstractHttpEndpointCaller endpointCaller;
	private Object sendData;
	private IHostTransServiceDefinition serviceDefinition;
	private Object message;
//	private EndpointSystemLogger systemLogger = null;

	public MyHttpRFDoubleCallback(String requestUrl,
			AbstractHttpEndpointCaller endpointCaller,
			String endpointResponseCallbackId, AbstractHttpEndpoint endpoint,
			Object sendData,
			IHostTransServiceDefinition serviceDefinition,
			Object message) {
		this.requestUrl = requestUrl;
		this.endpointCaller = endpointCaller;
		this.endpointResponseCallbackId = endpointResponseCallbackId;
		this.endpoint = endpoint;
		this.sendData = sendData;
		this.serviceDefinition = serviceDefinition;
		this.message = message;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.primeton.components.api.httpclient.apache.nio.concurrent.FutureCallback#cancelled()
	 */
	public void cancelled() {
		IEndpointResponseCallback endpointResponseCallback = endpoint.getEndpointResponseCallbackManager().remove(endpointResponseCallbackId);
		if (endpointResponseCallback == null) {
			logger.warn("The endpoint-id '" + endpoint.getId() + "' and url '" + requestUrl + "' is cancelled.");
			return;
		}
//		systemLogger = new EndpointSystemLogger(endpointResponseCallback.getRequestMessage(),endpoint);
		String requestId = endpointResponseCallback.getRequestId();
		String msg = "The request-id '" + requestId + "' for endpoint-id '" + endpoint.getId() + "' and url '" + requestUrl + "' is cancelled.";
//		systemLogger.logSystemLogExit(requestId, true, "the url '" + requestUrl + "' is cancelled.");
		logger.warn(msg);
		Throwable ex = new RuntimeException(msg);
		AbstractHttpEndpointResponseCreator creator = (AbstractHttpEndpointResponseCreator) endpoint.getEndpointResponseGenerator();
		endpointResponseCallback.doInResponse(creator.create(requestId, ex, null));
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.primeton.components.api.httpclient.apache.nio.concurrent.FutureCallback#completed(java.lang.Object)
	 */
	public void completed(HttpResponse result) {
		int sc = result.getStatusLine().getStatusCode();
		if (HttpServletResponse.SC_OK != sc) {
			Exception e = new IllegalStateException("Status code '" + sc + "' for url '" + requestUrl + "'.");
			failed(e);
			return;
		} else {
			IEndpointResponseCallback endpointResponseCallback = endpoint.getEndpointResponseCallbackManager().remove(endpointResponseCallbackId);
			if (endpointResponseCallback == null) {
				logger.warn("Not found endpoint-response-callback '" + endpointResponseCallbackId + "', maybe it is timeout or cancelled.");
				return;
			}
//			systemLogger = new EndpointSystemLogger(endpointResponseCallback.getRequestMessage(),endpoint);
			String requestId = endpointResponseCallback.getRequestId();

			byte[] bytes;
			EndpointResponse endpointResponse = null;
			Object receivedObject = null;
			try {
				bytes = ByteArrayUtils.copyToByteArray(result.getEntity().getContent());
				String receivedData = new String(bytes, endpoint.getDefinition().getEncoding());
				int maxLength = endpoint.getDefinition().getMaxLength();
				if(receivedData.length() > maxLength){
					if (logger.isErrorEnabled()) {
						logger.error("[REQ_ID][" + requestId + "] HTTP Endpoint '" + endpoint.getId() + "' received data greater than max lenth ["+maxLength+"] .");
					}
					throw new BTPRuntimeException("HTTP Transport '" + endpoint.getId() + "' received data greater than max lenth ["+maxLength+"] .");
				}

				receivedData = endpointCaller.invokeReceivedHandler(receivedData);
				receivedObject = endpoint.getDataExchange().read(receivedData);
				endpointResponse = endpoint.getEndpointResponseGenerator().create(requestId, receivedObject, null);
			} catch (Throwable e) {
				if(null == endpointResponse) {
					endpointResponse = endpoint.getEndpointResponseGenerator().create(requestId, receivedObject, null);
				}
				endpointResponse.setThrowable(e);
			}
			endpointResponseCallback.doInResponse(endpointResponse);
		}
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.primeton.components.api.httpclient.apache.nio.concurrent.FutureCallback#failed(java.lang.Exception)
	 */
	public void failed(Exception ex) {
		logger.debug("失败的异常返回信息[" + ex + "]");
		if (ex instanceof java.net.ConnectException
				|| ex instanceof java.net.SocketTimeoutException
//				|| ex instanceof java.lang.IllegalStateException
				|| ex instanceof java.net.NoRouteToHostException) {
			try{
				Reconnet();
			} catch (UnsupportedEncodingException e) {
				// 删除无用的callback.
				IEndpointResponseCallback endpointResponseCallback = endpoint.getEndpointResponseCallbackManager().remove(endpointResponseCallbackId);
				if (endpointResponseCallback == null) {
					logger.warn("Not found endpoint-response-callback '" + endpointResponseCallbackId + "' for exception '" + e + "', maybe it is timeout or cancelled.");
					return;
				}
				String requestId = endpointResponseCallback.getRequestId();
				Object obj = endpointResponseCallback.getRequestMessage().getPayload();
				AbstractHttpEndpointResponseCreator creator = (AbstractHttpEndpointResponseCreator) endpoint.getEndpointResponseGenerator();
				endpointResponseCallback.doInResponse(creator.create(requestId, e, null));
			}
		}
		else{
			IEndpointResponseCallback endpointResponseCallback = endpoint.getEndpointResponseCallbackManager().remove(endpointResponseCallbackId);
			if (endpointResponseCallback == null) {
				logger.warn("Not found endpoint-response-callback '" + endpointResponseCallbackId + "' for exception '" + ex + "', maybe it is timeout or cancelled.");
				return;
			}
//			systemLogger = new EndpointSystemLogger(endpointResponseCallback.getRequestMessage(),endpoint);
			String requestId = endpointResponseCallback.getRequestId();
			Object obj = endpointResponseCallback.getRequestMessage().getPayload();
			AbstractHttpEndpointResponseCreator creator = (AbstractHttpEndpointResponseCreator) endpoint.getEndpointResponseGenerator();
//			systemLogger.logSystemLogExit(requestId, true, ex);
			endpointResponseCallback.doInResponse(creator.create(requestId, ex, null));
		}
	}

	public void Reconnet() throws UnsupportedEncodingException{
		logger.info("try Reconnet()");
		String ip;
		String port;
		String markcode;
		EndpointSystemLogger systemLogger = null;
		systemLogger = new EndpointSystemLogger((Message) message,endpoint);

		String ReUrl;
		boolean isroute=(Boolean)DataPoolUtil.getData((Message)message, "$outsys.isroute");
		logger.debug("$outsys.isroute:[" + isroute + "]");
		if(isroute){
			ReUrl=(String)DataPoolUtil.getData((Message)message, "$outsys.url");
		}else{
			ip = endpoint.getDefinition().getExtProperty().getEntryValue("IP");
			port = endpoint.getDefinition().getExtProperty().getEntryValue("Port");
			markcode = endpoint.getDefinition().getExtProperty().getEntryValue("MarkCode");
			ReUrl="http://"+ip+":"+port+"/"+markcode;
		}

		logger.info("ReUrl:" + ReUrl);
		HttpAsyncClient client = endpoint.getHttpClient();
		HttpPost httpPost = new HttpPost(ReUrl);
		AbstractHttpEntity entity = null;

		IHttpEndpointDefinition definition;
		boolean isBodyRequest;
		definition = endpoint.getDefinition();
		isBodyRequest = definition.getRequestParamName() == null ? true : false;
		if (isBodyRequest) {
			if (sendData != null) {
				if(sendData instanceof byte[]) {
					entity = new NByteArrayEntity((byte[])sendData);
				}else{
					entity = new NStringEntity(sendData.toString(), definition.getEncoding());
				}
				entity.setContentType(definition.getRequestContentType());
			}
		} else {
			if (definition.getRequestParamName() != null) {
				List<NameValuePair> nvps = new ArrayList<NameValuePair>();
				if(sendData instanceof byte[]){
					sendData = new String((byte[])sendData,definition.getEncoding());
				}
				nvps.add(new BasicNameValuePair(definition.getRequestParamName(), sendData.toString()));
				entity = new NUrlEncodedFormEntity(nvps, definition.getEncoding());
			}
		}
		httpPost.setEntity(entity);
		systemLogger.logOriginalMessage(endpointResponseCallbackId, sendData, true);
		logger.debug("开始重新连接新的URL[" +ReUrl+ "]");

		client.execute(httpPost, new HttpResponseFutureCallback(ReUrl, endpointCaller, endpointResponseCallbackId, endpoint));
	}

}

/*
 * 修改历史
 * $Log: HttpResponseFutureCallback.java,v $
 * Revision 1.2  2013/09/06 08:13:00  wuyh
 * update:代码编码转换为UTF-8
 *
 * Revision 1.1  2013/09/03 11:41:17  liuxiang
 * add:添加BTP的项目
 *
 * Revision 1.2  2011/04/12 02:35:02  wangwb
 * Update:增加异常处理
 *
 * Revision 1.1  2011/03/31 10:08:56  wangwb
 * Update:修改性能测试中发现的问题
 *
 */