<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="PSBC-CD173" category="message" create-date="2021-08-21 15:17:58" version="*******">
  <message-definition bitmap-format="Binary" bitmap-length="0" bitmap-type="unionpay" category="json" filter-null="false" id="XMWEB_COM_SubQryRES" name="响应报文" namespace="prov3502_com.XMWEB_COM_SubQryRES" orderly="false" xml-pack-simple-node="false" xml-pack-statement="true" nodef-json="false">
    <message-ref display-name="head" name="head" ref="common_head_res" ref-path="" seqno="0">
      <description></description>
    </message-ref>
    <group-message-item display-name="body" name="body" seqno="1" xml-prefix="">
      <repeated-item display-name="list" name="list" out-key="true" repeat-field="" repeat-times="-1" seqno="0">
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR21" display-name="委托单位代码-STR21" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="merch_id" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR22" display-name="业务代码-STR22" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="ope_cd" pad-char="0x20" seqno="1" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR23" display-name="子业务名-STR23" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="sub_ope_nm" pad-char="0x20" seqno="2" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR24" display-name="SP商户代码-STR24" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="sp_merch_id" pad-char="0x20" seqno="3" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR25" display-name="商家代码-STR25" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="merch_inst_id" pad-char="0x20" seqno="4" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR26" display-name="行业机构代码-STR26" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="busi_merch_id" pad-char="0x20" seqno="5" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR27" display-name="账单代码-STR27" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="bill_id" pad-char="0x20" seqno="6" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR28" display-name="委托单位业务开关-STR28" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="sub_merch_on_off_fg" pad-char="0x20" seqno="7" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR29" display-name="开办日期-STR29" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="open_dt" pad-char="0x20" seqno="8" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR30" display-name="白名单标志-STR30" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="white_list_fg" pad-char="0x20" seqno="9" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR31" display-name="摘要名称-STR31" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="summ_cd" pad-char="0x20" seqno="10" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description>委托单位业务开关|not null 0-关1-开</description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR32" display-name="公司摘要-STR32" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="cpcb_summ_cd" pad-char="0x20" seqno="11" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR33" display-name="代收频率-STR33" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="pay_limit_num" pad-char="0x20" seqno="12" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description>（界面分2个输入项）&#xD;
6位字符串，同银联报文定义：扣款时间单位（2字节）+扣款次数（4字节），&#xD;
1、扣款时间单位：&#xD;
（1）00：不限制代收频率&#xD;
（2）01：年&#xD;
（3）02：季&#xD;
（4）03：月&#xD;
（5）04：周&#xD;
（6）05：日&#xD;
（7）其他取值：保留使用&#xD;
2、扣款次数：&#xD;
表示扣款时间单位内允许的最大扣款次数（指成功交易次数）。当扣款时间单位为000000（不限制代收频率）时，表示不限制扣款次数，发卡方可忽略扣款次数。&#xD;
例如：每月最多可扣款10次，则扣款时间单位为03（月），扣款次数为0010，代收频率取值为030010。</description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR34" display-name="最小交易金额-STR34" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="min_pay_at" pad-char="0x20" seqno="13" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description>12为字符串，以分为单位，前补0</description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR35" display-name="最大交易金额-STR35" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="max_pay_at" pad-char="0x20" seqno="14" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description>12为字符串，以分为单位，前补0</description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR36" display-name="委托关系期限-STR36" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="due_term" pad-char="0x20" seqno="15" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description>3位字符串，单位为月份，000表示长期有效；其他取值表示从建立委托交易日开始计算的有效期月份数；委托关系限期最多为999</description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR37" display-name="滞纳金比例-STR37" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="delay_rate" pad-char="0x20" seqno="16" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR38" display-name="子业务开通标志-STR38" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="flag01" pad-char="0x20" seqno="17" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <message-item align="right" dict-id="STR39" display-name="子业务代码-STR39" field-type="normal" is-required="false" name="sub_ope_id" pad-char="0x20" seqno="18" validate-rule="" value-mode="normal" xml-field-type="VALUE" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
      </repeated-item>
      <ext-property/>
    </group-message-item>
  </message-definition>
</configuration>