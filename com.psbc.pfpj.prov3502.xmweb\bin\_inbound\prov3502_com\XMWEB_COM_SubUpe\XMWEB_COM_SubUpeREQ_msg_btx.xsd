<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_com.XMWEB_COM_SubUpeREQ" xmlns:common_head_req="prov3502_common.common_head_req" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_com.XMWEB_COM_SubUpeREQ">
    <xs:import namespace="prov3502_common.common_head_req" schemaLocation="../prov3502_common/common_head_req_msg_btx.xsd"/>
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="sub_ope_nm" nillable="true" type="xs:string"/>
            <xs:element name="sp_merch_id" nillable="true" type="xs:string"/>
            <xs:element name="merch_inst_id" nillable="true" type="xs:string"/>
            <xs:element name="busi_merch_id" nillable="true" type="xs:string"/>
            <xs:element name="bill_id" nillable="true" type="xs:string"/>
            <xs:element name="sub_merch_on_off_fg" nillable="true" type="xs:string"/>
            <xs:element name="open_dt" nillable="true" type="xs:string"/>
            <xs:element name="white_list_fg" nillable="true" type="xs:string"/>
            <xs:element name="summ_cd" nillable="true" type="xs:string"/>
            <xs:element name="cpcb_summ_cd" nillable="true" type="xs:string"/>
            <xs:element name="pay_limit_num" nillable="true" type="xs:string"/>
            <xs:element name="min_pay_at" nillable="true" type="xs:string"/>
            <xs:element name="max_pay_at" nillable="true" type="xs:string"/>
            <xs:element name="due_term" nillable="true" type="xs:string"/>
            <xs:element name="delay_rate" nillable="true" type="xs:string"/>
            <xs:element name="flag01" nillable="true" type="xs:string"/>
            <xs:element name="last_modify_tlr_id" nillable="true" type="xs:string"/>
            <xs:element name="last_modify_inst_id" nillable="true" type="xs:string"/>
            <xs:element name="last_modify_dt" nillable="true" type="xs:string"/>
            <xs:element name="sub_ope_id" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="XMWEB_COM_SubUpeREQ">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="common_head_req:common_head_req"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
