<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="xmspf.xmspf_msghead_res" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="xmspf.xmspf_msghead_res">
    <xs:complexType name="statecode">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="执行结果" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="msg">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="执行结果描述" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="head">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="statecode" nillable="true" type="statecode"/>
            <xs:element name="msg" nillable="true" type="msg"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="xmspf_msghead_res">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="head"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
