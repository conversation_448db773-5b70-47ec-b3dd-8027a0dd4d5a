<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="Administrator" category="message" create-date="2021-12-02 11:03:25" version="7.0.0.0">
  <message-definition appear-type="none" bitmap-format="Binary" bitmap-length="0" bitmap-type="unionpay" category="delimiter" delimiter="|" filter-null="false" id="xmspf_ds_head" name="xmspf_ds_head" namespace="com.xmspf_ds_head" orderly="false" xml-pack-simple-node="false" xml-pack-statement="true" nodef-json="false">
    <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="MERCH_ID" display-name="委托单位代码" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="委托单位代码" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
      <ext-property/>
      <description></description>
    </message-item>
    <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="OPE_CD" display-name="业务代码" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="业务代码" pad-char="0x20" seqno="1" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
      <ext-property/>
      <description></description>
    </message-item>
    <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="BANK_NO" display-name="银行编码" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="3" modify-value="0" name="银行编码" pad-char="0x20" seqno="2" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-type="ASCII" vary-field-length="3" vary-length-type="fixedlength" message-field-type="ASCII">
      <ext-property/>
      <description></description>
    </message-item>
    <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="OUTSYS_TX_CODE" display-name="外系统交易码" field-type="transCode" is-recheck="false" is-required="false" is-secure="false" length="3" modify-value="0" name="外系统交易码" pad-char="0x20" seqno="3" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-type="ASCII" vary-field-length="3" vary-length-type="fixedlength" message-field-type="ASCII">
      <ext-property/>
      <description></description>
    </message-item>
    <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="TX_DATE" display-name="交易方日期" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="交易方日期" pad-char="0x20" seqno="4" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
      <ext-property/>
      <description></description>
    </message-item>
  </message-definition>
</configuration>