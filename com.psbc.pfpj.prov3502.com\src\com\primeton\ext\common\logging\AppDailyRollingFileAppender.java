package com.primeton.ext.common.logging;

import java.io.File;
import java.io.IOException;

import org.apache.log4j.DailyRollingFileAppender;

import com.eos.system.utility.FilenameUtil;
import com.primeton.ext.system.embedded.EmbeddedSystemCache;

public class AppDailyRollingFileAppender extends DailyRollingFileAppender {

	public synchronized void setFile(String fileName, boolean append,
			boolean bufferedIO, int bufferSize) throws IOException {
		String fileNameMerge = addWorkDir(fileName);
		File parent = new File(fileNameMerge).getParentFile();
		if ((parent != null) && (!(parent.exists())) && (!(parent.mkdir()))) {
			throw new IOException("Parent dir can not create!");
		}

		super.setFile(fileNameMerge, append, bufferedIO, bufferSize);
	}

	protected static String addWorkDir(String fileName) {
		if (fileName == null)
			return null;

		if (FilenameUtil.isAbsolutePath(fileName))
			return fileName;

		String appWorkPath = null;
		try {
			appWorkPath = EmbeddedSystemCache.getInstance().getServerWorkDir();
		} catch (Exception ignore) {
		}
		if (appWorkPath == null)
			return fileName;

		return new File(appWorkPath, fileName).getAbsolutePath();
	}

}
