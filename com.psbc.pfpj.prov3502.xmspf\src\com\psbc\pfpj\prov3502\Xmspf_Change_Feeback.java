/**
 * 
 */
package com.psbc.pfpj.prov3502;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import com.eos.system.annotation.Bizlet;
import com.pfpj.foundation.database.DatabaseExt;
import com.primeton.btp.api.core.logger.ILogger;
import com.primeton.btp.api.core.logger.LoggerFactory;
import com.primeton.btp.api.engine.context.IKernelServiceContext;
import com.primeton.btp.api.message.DataPoolUtil;
import com.primeton.tip.org.springframework.integration.core.Message;
import com.psbc.pfpj.prov3502.xmweb.MethodUtil;
import com.psbc.pfpj.prov3502.xmweb.NoneType;

/**
 * <AUTHOR>
 * @date 2021-09-08 08:55:14
 *
 */
@Bizlet("")
public class Xmspf_Change_Feeback {
	private static ILogger log = LoggerFactory.getLogger(Xmspf_Change_Feeback.class);
	private MethodUtil msg = new MethodUtil();
	
	String sys_id = "350200940109";
	
	private final static String ENCODING="UTF-8";
	@Bizlet("监管账户明细查询")
	public void Change_Feeback(IKernelServiceContext context){
		long start = System.currentTimeMillis();
		//获取容器
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		String message_id = (String) requestMessage.getHeaders().get("$btp.message.id");
		Map<String, Object> mapData = DataPoolUtil.getDataPool(requestMessage);
		for(Map.Entry<String,Object> m : mapData.entrySet()){
			log.debug("["+message_id+"] " + m.getKey() + ": " + m.getValue());
		}
		//获取监管账户
		String merch_id = NoneType.isToString(DataPoolUtil.getData(requestMessage, "MERCH_ID"));
		log.debug("merch_id=" + merch_id);
		String ope_cd = NoneType.isToString(DataPoolUtil.getData(requestMessage, "OPE_CD"));
		log.debug("ope_cd=" + ope_cd);
//		String merch_id = GetProperties.merch_id;
//		String ope_cd = GetProperties.ope_cd;
//		String sys_id = GetProperties.sysID;
		
		String tran_dt = String.valueOf(DataPoolUtil.getData(requestMessage, "TX_DATE"));
		log.info("=======tran_dt="+tran_dt);
		String bgn_date=tran_dt;
		String end_date=tran_dt;
		
		HashMap<String,Object> hmp = new HashMap<String,Object>();
		hmp.put("merch_id",merch_id);
		hmp.put("ope_cd",ope_cd);

		ArrayList<String> time_stamp = new ArrayList<String>();//明细时间
		ArrayList<String> ins_date = new ArrayList<String>();//记账日期
		ArrayList<String> vchr_no = new ArrayList<String>();//明细流水号
		ArrayList<String> open_unit = new ArrayList<String>();//开户银行代码
		ArrayList<String> open_unit_name = new ArrayList<String>();//开户网点名称
		ArrayList<String> sub_acct = new ArrayList<String>();//账号
		ArrayList<BigDecimal> tran_amt = new ArrayList<BigDecimal>();//交易金额
		ArrayList<String> debit_credit_flag = new ArrayList<String>();//金额变动类型
		ArrayList<BigDecimal> add1_bal = new ArrayList<BigDecimal>();//账户余额
		ArrayList<String> ops_bankno = new ArrayList<String>();//对方账户银行代码
		ArrayList<String> ops_bank_name = new ArrayList<String>();//对方账户开户网点名称
		ArrayList<String> ops_acct_name = new ArrayList<String>();//对方账户名
		ArrayList<String> ops_account = new ArrayList<String>();//对方账户号
		ArrayList<String> postscript = new ArrayList<String>();//备注
		
		ArrayList<String> send_datetime = new ArrayList<String>();//记账日期
		ArrayList<String> send_accname = new ArrayList<String>();//监管账户名称
		ArrayList<String> send_transq = new ArrayList<String>();//明细流水号
		ArrayList<String> send_type = new ArrayList<String>();//金额变动类型
		ArrayList<String>send_postscript = new ArrayList<String>();//备注
		ArrayList<String>send_tran_amt = new ArrayList<String>();//交易金额
		ArrayList<String>send_add1_bal = new ArrayList<String>();//账户余额
		
		String account="";
		String acct_name="";
		String pbc_brh_id="";
		Map<String,String> tmp = new HashMap<String,String>();//临时记录监管账号名称
		//获取监管账户列表下所有监管账户
		log.info("hmp="+hmp);
		Object[] result_data1 = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.select_cpab_acc_id", hmp);
		log.info("监管账户数量="+result_data1.length);
		if (result_data1.length <= 0){
			DataPoolUtil.addData(requestMessage, "RET_CODE", "9999");
			DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "未查到任何监管账户");
			return;
		}
		int all_num=0;
		for(int x=0;x<result_data1.length;x++){
			HashMap map = (HashMap) result_data1[x];
			account = map.get("cpab_acc_id").toString();
			acct_name = map.get("acct_nm").toString();
			tmp.put(account,acct_name);
			pbc_brh_id = map.get("pbc_brh_id").toString();
			log.info("监管账号Xmspf_Change_Feeback.account=["+account+"]bgn_date=["+bgn_date+"]end_date=["+end_date+"]");
			int sflag=1;
			String start_num="0";
			int y=0;
			while(sflag==1){
				y++;
				if(y>1000){
					log.error("系统出错,死循环");
					break;
				}
				log.info(" account=["+account+"]sflag=["+sflag+"]start_num=["+start_num+"]");
				DataPoolUtil.addData(requestMessage, "CLI_SERIAL_NO", System.currentTimeMillis());
				DataPoolUtil.addData(requestMessage, "NOTE_INFO", start_num);
				DataPoolUtil.addData(requestMessage, "ACCOUNT",account );
				DataPoolUtil.addData(requestMessage, "TX_CODE", "811776");	
				DataPoolUtil.addData(requestMessage, "BGN_DATE", bgn_date);
				DataPoolUtil.addData(requestMessage, "END_DATE", end_date);
				DataPoolUtil.addData(requestMessage, "OUTSYS_CODE", sys_id);
				DataPoolUtil.addData(requestMessage, "CHNL_CODE", "15");	
				try {
					CommonHost(context);
				} catch (Exception e) {
					// 如果出现问题 添加错误提示
					log.error("系统执行发生异常：" + PrintLog.getTrace(e));
					DataPoolUtil.addData(requestMessage, "RET_CODE", "9999");
					DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "调用后台服务错"+account);
					return ;
				} finally {
					log.info("准备--->接出服务--->发送给后台");
				}
				log.error("<<<<=====811776入账明细查询===结束=>");
				
				String respcode = DataPoolUtil.getData(requestMessage,"RET_CODE").toString();
				if(respcode.length() > 6){
					log.error("后台返回响应码超过6位=============>"+respcode);
					respcode=respcode.substring(respcode.length()-6);
				}
				if("000000".equals(respcode)||"CPCBG6".equals(respcode)||"0000".equals(respcode)){
					String total = DataPoolUtil.getData(requestMessage, "DET_CNT").toString();//明细笔数
					String zp_tran_sq=null;//中平流水号
					String zp_amt=null;//中平金额
		
					log.debug("DET_CNT="+total);
					all_num=all_num+Integer.valueOf(total);
					if(Integer.valueOf(total)>0){//存在成功记录
						start_num=String.valueOf(Integer.valueOf(start_num)+Integer.valueOf(total));
						log.info("记录数："+start_num);

						if(Integer.valueOf(start_num)==1){	
							time_stamp.add((String)DataPoolUtil.getData(requestMessage,"TIME_STAMP"));
							ins_date.add((String)DataPoolUtil.getData(requestMessage,"INS_DATE"));
							vchr_no.add((String)DataPoolUtil.getData(requestMessage,"STR50"));
//							open_unit.add((String)DataPoolUtil.getData(requestMessage,"OPEN_UNIT"));
							open_unit_name.add((String)DataPoolUtil.getData(requestMessage,"OPEN_UNIT_NAME"));
							sub_acct.add((String)DataPoolUtil.getData(requestMessage,"SUB_ACCT"));
							tran_amt.add((BigDecimal)DataPoolUtil.getData(requestMessage,"TRAN_AMT"));
							debit_credit_flag.add((String)DataPoolUtil.getData(requestMessage,"DEBIT_CREDIT_FLAG"));
							add1_bal.add((BigDecimal)DataPoolUtil.getData(requestMessage,"ADD1_BAL"));
							ops_bankno.add((String)DataPoolUtil.getData(requestMessage,"OPS_BANKNO"));
							ops_bank_name.add((String)DataPoolUtil.getData(requestMessage,"OPS_BANK_NAME"));
							ops_acct_name.add((String)DataPoolUtil.getData(requestMessage,"OPS_ACCT_NAME"));
							ops_account.add((String)DataPoolUtil.getData(requestMessage,"OPS_ACCOUNT"));
							postscript.add((String)DataPoolUtil.getData(requestMessage,"POSTSCRIPT"));
						}else{
							time_stamp.addAll((ArrayList<String>)DataPoolUtil.getData(requestMessage,"TIME_STAMP")); 
							ins_date.addAll((ArrayList<String>)DataPoolUtil.getData(requestMessage,"INS_DATE")); 
							vchr_no.addAll((ArrayList<String>)DataPoolUtil.getData(requestMessage,"STR50"));
//							open_unit.addAll((ArrayList<String>)DataPoolUtil.getData(requestMessage,"OPEN_UNIT"));
							open_unit_name.addAll((ArrayList<String>)DataPoolUtil.getData(requestMessage,"OPEN_UNIT_NAME"));
							sub_acct.addAll((ArrayList<String>)DataPoolUtil.getData(requestMessage,"SUB_ACCT"));
							tran_amt.addAll((ArrayList<BigDecimal>)DataPoolUtil.getData(requestMessage,"TRAN_AMT"));
							debit_credit_flag.addAll((ArrayList<String>)DataPoolUtil.getData(requestMessage,"DEBIT_CREDIT_FLAG"));
							add1_bal.addAll((ArrayList<BigDecimal>)DataPoolUtil.getData(requestMessage,"ADD1_BAL"));
							ops_bankno.addAll((ArrayList<String>)DataPoolUtil.getData(requestMessage,"OPS_BANKNO"));
							ops_bank_name.addAll((ArrayList<String>)DataPoolUtil.getData(requestMessage,"OPS_BANK_NAME"));
							ops_acct_name.addAll((ArrayList<String>)DataPoolUtil.getData(requestMessage,"OPS_ACCT_NAME"));
							ops_account.addAll((ArrayList<String>)DataPoolUtil.getData(requestMessage,"OPS_ACCOUNT"));
							postscript.addAll((ArrayList<String>)DataPoolUtil.getData(requestMessage,"POSTSCRIPT"));
						}
						log.info("vchr_no.size()："+vchr_no.size());
						log.info("后台返回的入账查询的中平流水list - vchr_no*************"+vchr_no);
						
						if(Integer.valueOf(total) == 10){
							sflag=1;
						}else{
							sflag=0;
						}
						log.info("循环标志==" +sflag+"监管账户start_num=="+ start_num);
					}else{
						sflag=0;
					}
					DataPoolUtil.deleteData(requestMessage, "TIME_STAMP");
					DataPoolUtil.deleteData(requestMessage, "SEQ_STR");
					DataPoolUtil.deleteData(requestMessage, "FILLER2");
					DataPoolUtil.deleteData(requestMessage, "SERIAL_SEQNO");
					DataPoolUtil.deleteData(requestMessage, "OPS_SUBACCT");
					DataPoolUtil.deleteData(requestMessage, "TELLER_CODE");
					DataPoolUtil.deleteData(requestMessage, "BARGAINING_FASHION");
					DataPoolUtil.deleteData(requestMessage, "SUB_ACCT");
					DataPoolUtil.deleteData(requestMessage, "RET_EXPLAIN");
					DataPoolUtil.deleteData(requestMessage, "RET_CODE");
					DataPoolUtil.deleteData(requestMessage, "CHG_CHECKER");
					DataPoolUtil.deleteData(requestMessage, "STR50");
					DataPoolUtil.deleteData(requestMessage, "ORG_CLR_DATE");
					DataPoolUtil.deleteData(requestMessage, "AUTH_OPERATOR");
					DataPoolUtil.deleteData(requestMessage, "ORG_TX_CODE");
					DataPoolUtil.deleteData(requestMessage, "CHNL_ID");
					DataPoolUtil.deleteData(requestMessage, "CRED_TYPE");
					DataPoolUtil.deleteData(requestMessage, "REGI_TYPE");
					DataPoolUtil.deleteData(requestMessage, "LOCALTIME");
					DataPoolUtil.deleteData(requestMessage, "DET_CNT");
					DataPoolUtil.deleteData(requestMessage, "INS_DATE");
					DataPoolUtil.deleteData(requestMessage, "VCHR_NO");
					DataPoolUtil.deleteData(requestMessage, "OPEN_UNIT");
					DataPoolUtil.deleteData(requestMessage, "OPEN_UNIT_NAME");
					DataPoolUtil.deleteData(requestMessage, "TRAN_AMT");
					DataPoolUtil.deleteData(requestMessage, "DEBIT_CREDIT_FLAG");
					DataPoolUtil.deleteData(requestMessage, "ADD1_BAL");
					DataPoolUtil.deleteData(requestMessage, "OPS_BANKNO");
					DataPoolUtil.deleteData(requestMessage, "OPS_BANK_NAME");
					DataPoolUtil.deleteData(requestMessage, "OPS_ACCT_NAME");
					DataPoolUtil.deleteData(requestMessage, "OPS_ACCOUNT");
					DataPoolUtil.deleteData(requestMessage, "POSTSCRIPT");
					
				}else{
					DataPoolUtil.addData(requestMessage, "RESPCODE", "9999");
					DataPoolUtil.addData(requestMessage, "RET_CODE", "9999");
					DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "监管账户："+account+"明细查询失败，请重试");
//					return ;
					break;
				}
			}
			
			
		}
		if(all_num==0){
			//log.info("监管账户："+account+"查询无明细，不发送委托方");
			log.info("监管账户上送流水："+account+",委托方返回响应信息："+DataPoolUtil.getData(requestMessage, "STR50"));
			DataPoolUtil.addData(requestMessage, "RESPCODE", "9999");
			DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "监管账户："+account+"查询无明细，不发送委托方");
			return ;
		}
		for(int x=0;x<vchr_no.size();x++){
			String datetime=time_stamp.get(x);
			send_datetime.add(x,datetime.substring(0, 4) + "-" + datetime.substring(4, 6)+ "-" + datetime.substring(6, 8) + " "+datetime.substring(8, 10)+":"+datetime.substring(10, 12)+":"+datetime.substring(12, 14));
			//send_accname.add(x,tmp.get(x));
			send_transq.add(x,ins_date.get(x)+vchr_no.get(x));
			open_unit.add(x,pbc_brh_id);
			if("1".equals(debit_credit_flag.get(x))){
				send_type.add(x,"1");
			}else{
				send_type.add(x,"2");
			}
			if(postscript.get(x).length()==0){
				send_postscript.add(x,"预售资金监管账户资金变动情况反馈");
			}else{
				send_postscript.add(x,"预售资金监管账户资金变动情况反馈."+postscript.get(x));
			}
			/*
			 * ********优化
			 * 金额去空格
			 */
			//send_tran_amt.add(x,String.format("%16.2f", tran_amt.get(x)));
			//send_add1_bal.add(x,String.format("%16.2f", add1_bal.get(x)));
			send_tran_amt.add(x,String.format("%16.2f", tran_amt.get(x)).trim());
			send_add1_bal.add(x,String.format("%16.2f", add1_bal.get(x)).trim());
		}
		for(int x=0;x<sub_acct.size();x++){
			log.info("监管账户："+sub_acct.get(x)+",监管账户名称："+tmp.get(sub_acct.get(x)));
			send_accname.add(x,tmp.get(sub_acct.get(x)));
		}
		//报文头赋值
		DataPoolUtil.addData(requestMessage, "TRAN_TYPE", "20002"); 
		DataPoolUtil.addData(requestMessage, "OPR_NAME", "ycyh"); 
		DataPoolUtil.addData(requestMessage, "PASSWORD", "xmyc2016"); 
		DataPoolUtil.addData(requestMessage, "TLR_NAME", ""); 
		DataPoolUtil.addData(requestMessage, "STR31", ""); 
		//接口类型赋值
		DataPoolUtil.addData(requestMessage, "INTER_CODE", "xmspf_20002"); 
		
		DataPoolUtil.addData(requestMessage, "TX_DATE",send_datetime);//记账日期时间
		DataPoolUtil.addData(requestMessage, "SEQNO",send_transq);//明细流水号
		DataPoolUtil.addData(requestMessage, "BANK_NO",open_unit);//开户银行代码
		DataPoolUtil.addData(requestMessage, "OPEN_UNIT_NAME",open_unit_name);//开户网点名称
		DataPoolUtil.addData(requestMessage, "ACCT_NAME",send_accname);//账户名
//		DataPoolUtil.addData(requestMessage, "ACCOUNT",sub_acct);//账户号
		DataPoolUtil.addData(requestMessage, "STR16",sub_acct);//账户号
		DataPoolUtil.addData(requestMessage, "STR41",send_tran_amt);//交易金额<amount>            0.00</amount>
		DataPoolUtil.addData(requestMessage, "AMNT_TYPE",send_type);//金额变动类型
		DataPoolUtil.addData(requestMessage, "STR20","");//入账来源方式
		DataPoolUtil.addData(requestMessage, "STR42",send_add1_bal);//账户余额
		/*
		 * ********优化
		 * 去掉机构号和几个名称
		 */
		//DataPoolUtil.addData(requestMessage, "PAY_BANK",ops_bankno);//对方账户银行
		//DataPoolUtil.addData(requestMessage, "STR33",ops_bank_name);//对方账户开户网点名称
		DataPoolUtil.addData(requestMessage, "PAY_BANK","");//对方账户银行
		DataPoolUtil.addData(requestMessage, "STR33","");//对方账户开户网点名称
		DataPoolUtil.addData(requestMessage, "STR32",ops_acct_name);//对方账户名
//		DataPoolUtil.addData(requestMessage, "ACCOUNT_NO",ops_account);//对方账户号
		DataPoolUtil.addData(requestMessage, "STR17",ops_account);//对方账户号
		DataPoolUtil.addData(requestMessage, "REMARKS",send_postscript);//备注（预售资金监管账户资金变动情况反馈）
			
		//发送报文
		try {
			log.info("调用20002方法前开始");
			requestMessage = context.getServiceRequest().getRequestMessage();
			mapData = DataPoolUtil.getDataPool(requestMessage);
			for(Map.Entry<String,Object> m : mapData.entrySet()){
				log.debug("["+message_id+"] " + m.getKey() + ": " + m.getValue());
			}
			log.info("调用20002方法前结束");
			HostToWTM20002(context);
			log.info("调用20002方法后开始");
			requestMessage = context.getServiceRequest().getRequestMessage();
			mapData = DataPoolUtil.getDataPool(requestMessage);
			for(Map.Entry<String,Object> m : mapData.entrySet()){
				log.debug("["+message_id+"] " + m.getKey() + ": " + m.getValue());
			}
			log.info("调用20002方法后结束");
			DataPoolUtil.addData(requestMessage, "RET_CODE", "0000");
			DataPoolUtil.addData(requestMessage, "RESPCODE", "0000");
			DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "交易成功");
			
			String respcode = (String) DataPoolUtil.getData(requestMessage, "STATE");//委托方响应结果
			if(respcode.equals("1")){//委托方响应成功
				log.info("监管账户上送流水："+account+",委托方返回响应信息："+DataPoolUtil.getData(requestMessage, "STR50"));
			}else{
				log.info("监管账户上送流水："+account+",委托方返回响应信息："+DataPoolUtil.getData(requestMessage, "STR50"));
				DataPoolUtil.addData(requestMessage, "RET_CODE", "9999");
				DataPoolUtil.addData(requestMessage, "RESPCODE", "9999");
				DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "交易失败");
			}
			
		} catch (Exception e) {
			// 如果出现问题 添加错误提示
			log.error("系统执行发生异常：" + PrintLog.getTrace(e));
			DataPoolUtil.addData(requestMessage, "RESPCODE", "9999");
			DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "1-调用xmspf_20002出错");
			return ;
		} finally {
			log.info("准备--->接出服务--->触发器响应");
		}
	
			
	}
		
	/**
	 * 通用调用后台，覆盖返回值
	 * @param context
	 * @return
	 */
	@Bizlet("通用调用后台")
	public static void CommonHost(IKernelServiceContext context) {
		//获取容器
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		log.debug("=========通用调用后台=========");
		DataPoolUtil.addData(requestMessage, "INTER_CODE", "OSTS001");
		HostServiceInvoker.invoke(context);
	}		
	
	/**
	 * 调用委托方4002接口
	 * @param HostToWTM4002
	 * @return
	 */
	@Bizlet("调用委托方xmspf_20002接口")
	public void HostToWTM20002(IKernelServiceContext context) {
		//获取容器
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		
		log.debug("=========调用xmspf_20002=========");
		DataPoolUtil.addData(requestMessage, "INTER_CODE", "xmspf_20002");
		HostServiceInvoker_202.invoke(context);
	}
}
