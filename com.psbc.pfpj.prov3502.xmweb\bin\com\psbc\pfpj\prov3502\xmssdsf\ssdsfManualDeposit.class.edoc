<map>
  <entry>
    <string>com.psbc.pfpj.prov3502.xmssdsf.ssdsfManualDeposit.DepositNote(com.primeton.btp.api.engine.context.IKernelServiceContext)</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;void com.psbc.pfpj.prov3502.xmssdsf.ssdsfManualDeposit.DepositNote(IKernelServiceContext context)&lt;/b&gt;&lt;br/&gt;&lt;p&gt; 存款通知 &#x0D;
 DepositNote&#x0D;
&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
  <entry>
    <string>com.psbc.pfpj.prov3502.xmssdsf.ssdsfManualDeposit.error(com.primeton.btp.api.engine.context.IKernelServiceContext)</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;void com.psbc.pfpj.prov3502.xmssdsf.ssdsfManualDeposit.error(IKernelServiceContext context)&lt;/b&gt;&lt;br/&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
  <entry>
    <string>ssdsfManualDeposit</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;com.psbc.pfpj.prov3502.xmssdsf.ssdsfManualDeposit&lt;/b&gt;&lt;br/&gt;&lt;p&gt; 外联管理端-存款通知&#x0D;
&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
</map>