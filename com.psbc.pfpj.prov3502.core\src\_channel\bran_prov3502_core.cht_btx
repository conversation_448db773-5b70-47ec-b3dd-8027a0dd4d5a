<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="PSBC-CD173" category="transport-tcp" create-date="2021-04-25 15:41:26" version="*******">
  <transport-tcp id="bran_prov3502_core">
    <name>bran_prov3502_core</name>
    <mode>DUPLEX</mode>
    <description>后台到外联通用接入</description>
    <host>0.0.0.0</host>
    <port>9511</port>
    <max-socket-size>1000</max-socket-size>
    <is-short-socket>false</is-short-socket>
    <transport-request-creator>com.primeton.btp.spi.transport.tcp.CPlatformTcpTransportRequestCreator</transport-request-creator>
    <data-exchange-class></data-exchange-class>
    <transport-request-handler>
      <min-size>50</min-size>
      <max-size>50</max-size>
      <increase-size>1</increase-size>
      <keep-alive-time>60</keep-alive-time>
      <checkout-timeout>5</checkout-timeout>
      <factory-class></factory-class>
    </transport-request-handler>
    <work-threads>
      <min-size>50</min-size>
      <max-size>50</max-size>
      <keep-alive-time>60</keep-alive-time>
      <queue-size>1000</queue-size>
      <rejected-policy></rejected-policy>
    </work-threads>
    <work-threads-ref></work-threads-ref>
    <ext-property/>
    <ext-class></ext-class>
    <encoding>GBK</encoding>
    <is-record-message>null</is-record-message>
    <system-field-ref>cplatform</system-field-ref>
    <message-ending mode="2">
      <offset>0</offset>
      <length>6</length>
      <initial-bytes-to-strip>6</initial-bytes-to-strip>
      <corrected-value>0</corrected-value>
      <length-acquire-mode>2</length-acquire-mode>
    </message-ending>
    <max-length>20480</max-length>
    <is-multi>false</is-multi>
    <transport-num>5</transport-num>
    <endpoint-num>5</endpoint-num>
    <endpoint-ip>127.0.0.1</endpoint-ip>
    <endpoint-port>9003</endpoint-port>
    <is-secure>null</is-secure>
    <heart-beat-msg>com.primeton.btp.spi.transport.tcp.HeartBeatTest</heart-beat-msg>
    <serialno-rule>UUID</serialno-rule>
  </transport-tcp>
</configuration>