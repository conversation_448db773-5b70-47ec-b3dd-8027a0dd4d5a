<?xml version="1.0" encoding="UTF-8"?>
<configuration author="PSBC-CD173" category="transaction" create-date="2021-08-20 14:54:15" version="*******" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" xmlns="http://www.primeton.com/btp/cfg" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<transaction id="XMWEB_COM_MODel">
		<name>XMWEB_COM_MODel</name>
		<transport-id>bran_prov3502_xmweb</transport-id>
		<description>管理端发起的业务代码委托机构关联删除</description>
		<timeout>0</timeout>
		<flow-id>_inbound.prov3502_com.XMWEB_COM_MODel.XMWEB_COM_MODel</flow-id>
		<req-message-id>XMWEB_COM_MODelREQ</req-message-id>
		<resp-message-id>XMWEB_COM_MODelRES</resp-message-id>
		<exception-message-id>XMWEB_COM_MODelEXCE</exception-message-id>
		<is-one-way>false</is-one-way>
		<process-type>REQUEST</process-type>
		<auto-reverse>false</auto-reverse>
		<is-record-transaction-journal>false</is-record-transaction-journal>
		<is-record-system-log>false</is-record-system-log>
		<is-secure>false</is-secure>
	    <auto-assign>true</auto-assign>
		<ext-property>
		</ext-property>
		<ext-class></ext-class>
		 <rework-tx-code>rework001</rework-tx-code>
   		 <rework-sequence>P</rework-sequence>
    	 <rework-times>3</rework-times>
   		 <rework-priority>0</rework-priority>
    	 <jnlFlag>false</jnlFlag>
		<msg2file>false</msg2file>
		<check-type>NO</check-type>
	</transaction>
</configuration>
