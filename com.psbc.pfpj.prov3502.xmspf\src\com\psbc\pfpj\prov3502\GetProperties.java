/**
 * 
 */
package com.psbc.pfpj.prov3502;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Properties;

import com.eos.runtime.core.ApplicationContext;
import com.eos.system.annotation.Bizlet;

/**
 * <AUTHOR>
 * @date 2021-07-05 09:29:45
 *
 */
@Bizlet("")
public class GetProperties {
	public static String ip = "";
	public static int port = 0;
	public static String sysID = "";//外系统代码
	public static String ope_cd = "";
	public static String merch_id = "";
	public static String pdfPath = "";
	public static String cerPath = "";
	public static String userCardno = "";//cer持证人证件号码
	public static String userName = "";//cer持证人名称：厦门市住房和建设局
	
	
	
	static{
		LoadProperties();
	}
	
	public static Boolean LoadProperties(){
		String propertiesPath = ApplicationContext.getInstance().getApplicationUserWorkingPath()+
				"/com.psbc.pfpj.prov3502.xmspf/META-INF/xmspf.properties";
		try {
			Properties prop=new Properties();
			FileInputStream in;
			in = new FileInputStream(new File(propertiesPath));
			prop.load(new InputStreamReader(in,"UTF-8"));
			ip = prop.getProperty("ip");
			port =Integer.parseInt(prop.getProperty("port"));
			sysID = prop.getProperty("sysID");
			ope_cd = prop.getProperty("ope_cd");
			merch_id = prop.getProperty("merch_id");
			pdfPath = prop.getProperty("pdf_path");
			cerPath = prop.getProperty("cer_path");
			userName = prop.getProperty("user_name");
			userCardno = prop.getProperty("user_cardno");
			return true;
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return false;
		}
		
	}
}
