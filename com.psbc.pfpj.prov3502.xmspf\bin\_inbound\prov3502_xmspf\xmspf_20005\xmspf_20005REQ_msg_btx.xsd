<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_xmspf.xmspf_20005REQ" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xmspf_msghead_req="xmspf.xmspf_msghead_req" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_xmspf.xmspf_20005REQ">
    <xs:import namespace="xmspf.xmspf_msghead_req" schemaLocation="../xmspf/xmspf_msghead_req_msg_btx.xsd"/>
    <xs:complexType name="instructionno">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="指令流水号" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="instructionno" nillable="true" type="instructionno"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="content">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="xmspf_msghead_req:xmspf_msghead_req"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="xmspf_20005REQ">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="content" nillable="true" type="content"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
