<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="com.psbc.pfpj.prov3502.xmweb.Trading">
    <!-- 管理端发起的签约关系查询 -->
    <select id="query_common_0002" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			merch_id,
			ope_cd,
			corp_cd,
			pay_id,
			acc_id,
			acc_card_id,
			commi_dt,
			effect_dt,
			pre_commi_dt,
			pre_effect_dt,
			stat_cd
		from 
			tb_pay_commi_info
		where
			merch_id = #merch_id#
			and ope_cd = #ope_cd#
		<isNotEmpty property="corp_cd">and corp_cd = #corp_cd#</isNotEmpty>
        <isNotEmpty property="pay_id">and pay_id = #pay_id#</isNotEmpty>
        <isNotEmpty property="acc_id">and (acc_id = #acc_id# or acc_card_id = #acc_id#)</isNotEmpty>
    </select>
    <!-- 管理端发起的批量交易查询 -->
    <select id="query_common_0003" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			batch_id,
			tran_dt,
			plan_tran_dt,
			total_qt,
			total_at,
			exec_dt,
			req_file_name_tx,
			rsp_file_name,
			proc_fg,
			core_batch_id,
			succ_qt,
			succ_at,
			fail_qt,
			fail_at
		from 
			tb_batch_tran_ctrl
		where
			merch_id like '%'||#merch_id#||'%'
			and ope_cd like '%'||#ope_cd#||'%'
			and tran_dt between #begindt# and #enddt#
			<isNotEmpty property="proc_fg">and proc_fg = #proc_fg#</isNotEmpty>
		order by tran_dt,plan_tran_dt,batch_id desc
	</select>
    <!-- 管理端发起的批量明细交易查询 -->
    <select id="query_common_0004" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			batch_id,
			file_inacc_dt,
			pay_id,
			acc_id,
			summary,
			tran_at,
			ans_cd,
			ans_tx,
			proc_fg
		from 
			tb_batch_tran_dtl
		where
			tran_dt between #begindt# and #enddt#
			<isNotEmpty prepend="AND" property="merch_id">merch_id like '%'||#merch_id#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="ope_cd">ope_cd like '%'||#ope_cd#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="batch_id">batch_id = #batch_id#</isNotEmpty>
        <isNotEmpty prepend="AND" property="acc_id">acc_id = #acc_id#</isNotEmpty>
		order by batch_id desc
	</select>
    <!-- 管理端发起的缴费记录查询 -->
    <select id="query_common_0005" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			merch_id,
			ope_cd,
			tran_dt,
			tran_at,
			tran_stat_cd
		from 
			tb_int_txn_log
		where
			tran_dt between #begindt# and #enddt#
			<isNotEmpty prepend="AND" property="merch_id">merch_id like '%'||#merch_id#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="ope_cd">ope_cd like '%'||#ope_cd#||'%'</isNotEmpty>
		order by tran_dt desc
	</select>
    <!-- 管理端发起的一卡通签约不成功交易查询 _备份-->
    <select id="query_common_0006_bak" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select 
			merch_id,
			ope_cd,
			user_id,
			cust_name,
			host_rsp
		from 
			tb_3502_xmykt_record
		where
			merch_id like '%'||#merch_id#||'%'
			and ope_cd like '%'||#ope_cd#||'%'
			and tran_dt between #begindt# and #enddt#
			and record_id = 'UAF'</select>
    <!-- 管理端发起的一卡通签约不成功交易查询 -->
    <select id="query_common_0006" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">select 
			r.acc_card_id,
			r.sub_key,
			i.inst_nm,
			r.tran_dt,
			r.agent_key,
			r.record_id,
			r.item_id,
			r.trace_id,
			r.revole_dt,
			r.acc_card_id,
			r.invalid_dt,
			r.unite_id,
			r.merch_id,
			r.ope_cd,
			r.type_cd,
			r.user_id,
			r.revoke_case,
			r.fee_amt,
			r.fee_fint,
			r.fee_hand,
			r.fee_service,
			r.batch_id,
			r.batch_seq_no,
			r.paper_id,
			r.cust_name,
			r.cust_addr,
			r.cust_tel,
			r.post_code,
			r.host_rsp,
			r.fail_tx,
			r.misc_tx,
			r.home_addr,
			r.user_name,
			r.oth_msg1,
			r.oth_msg2,
			o.summ_cd
		from 
			tb_3502_xmykt_record r
			left join tb_merch_sub_ope o on o.sp_merch_id = r.unite_id
			left join tb_inst_info i on i.inst_id = r.sub_key
		where
			r.merch_id like '%'||#merch_id#||'%'
			and r.ope_cd like '%'||#ope_cd#||'%'
			and r.tran_dt between #begindt# and #enddt#
			and record_id = 'FFF'</select>
    <!-- 管理端发起的欠费库记录查询查询 -->
    <select id="query_common_0007" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			load_dt,
			seq_sq,
			ope_cd,
			merch_id,
			pay_id,
			acc_card_id,
			break_dt,
			owe_at,
			delay_at,
			tran_at,
			tran_dt,
			txn_sta
		from 
			tb_pay_owe
		where
			load_dt between #begindt# and #enddt#
			<isNotEmpty prepend="AND" property="merch_id">merch_id like '%'||#merch_id#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="ope_cd">ope_cd like '%'||#ope_cd#||'%'</isNotEmpty>
		order by load_dt desc
	</select>
    <!-- 管理端发起的外联批量处理日查询 -->
    <select id="query_common_0008" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			merch_id,
			ope_cd,
			tran_dt,
			merch_name,
			proc_memo,
			in_file_name,
			out_file_name,
			batch_id,
			tol_num,
			tol_amt,
			act_num,
			act_amt,
			proc_flag,
			proc_reason,
			load_time
		from 
			tb_batch_process_log
		where
			tran_dt between #begindt# and #enddt#
			<isNotEmpty prepend="AND" property="merch_id">merch_id like '%'||#merch_id#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="ope_cd">ope_cd like '%'||#ope_cd#||'%'</isNotEmpty>
        <isNotEmpty prepend="AND" property="batch_id">batch_id = #batch_id#</isNotEmpty>
			
			order by tran_dt desc
	</select>
    <!-- 管理端发起的签约关系历史查询 -->
    <select id="query_common_0009" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			tran_dt,
			tran_tm,
			tran_code,
			tran_memo,
			tran_inst_id,
			tran_inst_type,
			tran_tlr_id,
			merch_id,
			ope_cd,
			pay_id,
			stat_cd,
			acc_id,
			open_inst_id,
			acc_bank_flag,
			payer_id
		from 
			tb_pay_commi_info_his
		where
			merch_id = #merch_id#
			and ope_cd = #ope_cd#
			<isNotEmpty prepend="AND" property="pay_id">pay_id = #pay_id#</isNotEmpty>
		order by tran_dt desc,tran_tm desc
	</select>
</sqlMap>