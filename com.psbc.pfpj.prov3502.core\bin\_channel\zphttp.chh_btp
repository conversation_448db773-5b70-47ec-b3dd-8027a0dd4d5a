<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="PSBC-CD173" category="transport-http" create-date="2024-06-04 11:09:05" version="*******">
  <transport-http id="zphttp">
    <name>zphttp</name>
    <encoding>UTF-8</encoding>
    <exception-translator></exception-translator>
    <request-param-name></request-param-name>
    <jetty-continuation-timeout>30</jetty-continuation-timeout>
    <is-record-message>false</is-record-message>
    <is-secure>false</is-secure>
    <file-transmission>
      <transmission-mode>ftp</transmission-mode>
      <port>22</port>
    </file-transmission>
    <description>后台到外联通用http接入</description>
    <host>0.0.0.0</host>
    <port>9801</port>
    <context>/</context>
    <protocol-type>http</protocol-type>
    <data-exchange-class>com.primeton.btp.spi.transport.http.HttpDataExchange</data-exchange-class>
    <transport-request-creator>com.primeton.btp.spi.transport.http.HttpTransportRequestCreator</transport-request-creator>
    <transport-request-handler>
      <min-size>50</min-size>
      <max-size>50</max-size>
      <increase-size>1</increase-size>
      <keep-alive-time>60</keep-alive-time>
      <checkout-timeout>5</checkout-timeout>
      <factory-class></factory-class>
    </transport-request-handler>
    <work-threads>
      <min-size>50</min-size>
      <max-size>50</max-size>
      <keep-alive-time>60</keep-alive-time>
      <queue-size>0</queue-size>
      <rejected-policy></rejected-policy>
    </work-threads>
    <ext-property>
      <entry description="" key="MIN_THREADS" value="50"/>
      <entry description="" key="MAX_THREADS" value="50"/>
    </ext-property>
    <ext-class></ext-class>
    <max-length>512000</max-length>
    <is-verify-client>false</is-verify-client>
    <serialno-rule>UUID</serialno-rule>
    <target-system>99700040004</target-system>
  </transport-http>
</configuration>