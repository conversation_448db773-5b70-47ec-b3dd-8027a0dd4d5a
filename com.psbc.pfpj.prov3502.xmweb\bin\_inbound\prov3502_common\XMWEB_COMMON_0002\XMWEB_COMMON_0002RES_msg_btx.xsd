<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_common.XMWEB_COMMON_0002RES" xmlns:common_head_res="prov3502_common.common_head_res" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_common.XMWEB_COMMON_0002RES">
    <xs:import namespace="prov3502_common.common_head_res" schemaLocation="../prov3502_common/common_head_res_msg_btx.xsd"/>
    <xs:complexType name="list">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="merch_id" nillable="true" type="xs:string"/>
            <xs:element name="ope_cd" nillable="true" type="xs:string"/>
            <xs:element name="corp_cd" nillable="true" type="xs:string"/>
            <xs:element name="pay_id" nillable="true" type="xs:string"/>
            <xs:element name="acc_id" nillable="true" type="xs:string"/>
            <xs:element name="commi_dt" nillable="true" type="xs:string"/>
            <xs:element name="effect_dt" nillable="true" type="xs:string"/>
            <xs:element name="pre_commi_dt" nillable="true" type="xs:string"/>
            <xs:element name="pre_effect_dt" nillable="true" type="xs:string"/>
            <xs:element name="stat_cd" nillable="true" type="xs:string"/>
            <xs:element name="acc_card_id" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="list" nillable="true" type="list"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="XMWEB_COMMON_0002RES">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="common_head_res:common_head_res"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
