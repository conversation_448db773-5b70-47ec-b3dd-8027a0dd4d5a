<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_com.XMWEB_COM_MerchAddREQ" xmlns:common_head_req="prov3502_common.common_head_req" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_com.XMWEB_COM_MerchAddREQ">
    <xs:import namespace="prov3502_common.common_head_req" schemaLocation="../prov3502_common/common_head_req_msg_btx.xsd"/>
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="merch_nm" nillable="true" type="xs:string"/>
            <xs:element name="merch_on_off_fg" nillable="true" type="xs:string"/>
            <xs:element name="merch_inst_id" nillable="true" type="xs:string"/>
            <xs:element name="last_modify_tlr_id" nillable="true" type="xs:string"/>
            <xs:element name="last_modify_inst_id" nillable="true" type="xs:string"/>
            <xs:element name="last_modify_dt" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="XMWEB_COM_MerchAddREQ">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="common_head_req:common_head_req"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
