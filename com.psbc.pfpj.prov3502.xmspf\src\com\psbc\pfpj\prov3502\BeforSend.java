/**
 * 
 */
package com.psbc.pfpj.prov3502;

import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.eos.common.transaction.ITransactionManager;
import com.eos.common.transaction.TransactionManagerFactory;
import com.eos.system.annotation.Bizlet;
import com.pfpj.foundation.database.DatabaseExt;
import com.primeton.btp.api.core.logger.ILogger;
import com.primeton.btp.api.core.logger.LoggerFactory;
import com.primeton.btp.api.engine.context.IKernelServiceContext;
import com.primeton.btp.api.message.DataPoolUtil;
import com.primeton.btp.com.alibaba.fastjson.JSONObject;
import com.primeton.tip.org.springframework.integration.core.Message;
import com.psbc.pfpj.prov3502.xmweb.LogTableDAO;
import com.psbc.pfpj.prov3502.xmweb.NoneType;
import com.spire.pdf.PdfDocument;
import com.spire.pdf.fields.PdfField;
import com.spire.pdf.security.PdfSignature;
import com.spire.pdf.widget.PdfFormWidget;
import com.spire.pdf.widget.PdfSignatureFieldWidget;



/**
 * <AUTHOR>
 * @date 2020-8-24 09:12:33
 * 
 */
@Bizlet("厦门商品房")
public class BeforSend {

	private static ILogger log = LoggerFactory.getLogger(BeforSend.class);
	
	//更新批量处理的状态
	public static void  setRspInfo(Message<?> requestMessage,String resp_code,String respinfo ){
		DataPoolUtil.addData(requestMessage, "STATE", resp_code);
		DataPoolUtil.addData(requestMessage, "STR50",  respinfo);	
	}
	

	
	@Bizlet("设置数据")
	public static void SetData(IKernelServiceContext context) {
		// 获取容器
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		String message_id = (String) requestMessage.getHeaders().get("$btp.message.id");
		log.debug("["+message_id+"] For循环进行Map集合遍历--->");
		Map<String, Object> mapData = DataPoolUtil.getDataPool(requestMessage);
		for (Map.Entry<String, Object> m : mapData.entrySet()) {
			log.debug("["+message_id+"] " + m.getKey() + ";Value:" + m.getValue());
		}
		String tran_type = "";
		try {	
			String retcode = "";//函数返回值
			int ret=0;
			String inter_code =  String.valueOf(DataPoolUtil.getData(requestMessage,"INTER_CODE"));//服务编号
			if(NoneType.isNullOrEmpty(inter_code)){
				log.info("***报文头服务编号为空***");
				setRspInfo(requestMessage,"0","报文头服务编号为空");
				return;
			}
			String opr_name =  String.valueOf(DataPoolUtil.getData(requestMessage,"OPR_NAME"));//用户名
			if(NoneType.isNullOrEmpty(opr_name)){
				log.info("***报文头用户名为空***");
				setRspInfo(requestMessage,"0","报文头用户名为空");
				return;
	    	}
			
			String merch_id = GetProperties.merch_id; 
			String ope_cd = GetProperties.ope_cd;
			
			// 服务编号 INTER_CODE
			//String inter_code = String.valueOf(DataPoolUtil.getData(requestMessage,"INTER_CODE"));    		
			log.info("服务编号："+inter_code);
			if(inter_code.equals("20001")){
				tran_type = "监管账号验证";
				log.info("***监管账号验证:"+ inter_code);
				
				String accountname = DataPoolUtil.getData(requestMessage,"ACCT_NAME")==null?"":DataPoolUtil.getData(requestMessage,"ACCT_NAME").toString();//账户名称
				if(accountname.length()==0){
					log.info("***账户名称为空***");
					setRspInfo(requestMessage,"0","账户名称为空");
					return ;
				}
				String accountno = DataPoolUtil.getData(requestMessage,"ACCOUNT_NO")==null?"":DataPoolUtil.getData(requestMessage,"ACCOUNT_NO").toString();//账号
				if(accountno.length()==0){
					log.info("***账号为空***");
					setRspInfo(requestMessage,"0","账号为空");
					return ;
				}
				String bankid = DataPoolUtil.getData(requestMessage,"BANK_NO")==null?"":DataPoolUtil.getData(requestMessage,"BANK_NO").toString();//开户银行代码
				if(bankid.length()==0){
					log.info("***开户银行代码为空***");
					setRspInfo(requestMessage,"0","开户银行代码为空");
					return ;
				}
				String mark_flag ="";
				
				HashMap<String,Object> hmp = new HashMap<String,Object>();
				hmp.put("merch_id",merch_id);
				hmp.put("ope_cd",ope_cd);
				hmp.put("cpab_acc_id",accountno);
				log.info("hmp="+hmp);
				Object[] result_accmsg = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.check_acc", hmp);
				if (result_accmsg.length == 0){
					//监管账户信息不存，返回匹配失败
					mark_flag = "0";
				}else{
					HashMap map = (HashMap) result_accmsg[0];
					String instno = map.get("pbc_brh_id").toString();
					String acctname = map.get("acct_nm").toString();
					
					if(!accountname.equals(acctname)){
						log.info("账号["+accountno+"]-错误信息："+"账户名称-"+accountname+"不匹配后台查到的账户名称-"+acctname);
						mark_flag = "0";
					}else if(!bankid.equals(instno)){
						log.info("账号验证指令开户银行代码与账号不匹配,请联系银行业务人员进行核对!");
						mark_flag = "0";
					}else{
						mark_flag = "1";
					}
				}
				DataPoolUtil.addData(requestMessage, "MARK_FLAG", mark_flag);
				
				setRspInfo(requestMessage,"1","交易成功");
					
			} else if (inter_code.equals("20002")){
				log.info("***预售资金监管账户资金变动情况反馈:"+ inter_code);
			
			} else if(inter_code.equals("20003")){ //社保发起查询社保机构账户余额	
				tran_type = "预售资金监管账户支付指令";
				log.info("***预售资金监管账户支付指令:"+ inter_code);
				log.info("---------------------------验签---------------------------");
				String orgSignInfo = (String) DataPoolUtil.getData(requestMessage,"STR40");
				String str49 =  (String) DataPoolUtil.getData(requestMessage,"STR49");
				if (orgSignInfo == null) {
					setRspInfo(requestMessage,"0","签名信息有误");
					return;
				} else {
					String pubKey = KeyUtils.getPubKey();
					SM2Utils sm2Utils = new SM2Utils();
					if (!sm2Utils.verify(str49, orgSignInfo, pubKey)) {
						setRspInfo(requestMessage,"0","签名验证失败");
						return;
					}
				}
				
				JSONObject str49JsonObject = JSONObject.parseObject(str49);
				DataPoolUtil.addData(requestMessage, "TX_DATE", str49JsonObject.get("optdate"));//时间
				DataPoolUtil.addData(requestMessage, "SEQNO", str49JsonObject.get("instructionno"));//指令流水号
				DataPoolUtil.addData(requestMessage, "SETTL_AMT", new BigDecimal((String) str49JsonObject.get("amount")));//结算金额
				DataPoolUtil.addData(requestMessage, "INST_NO", str49JsonObject.get("bankid"));//开户行代码
				DataPoolUtil.addData(requestMessage, "INST_NAME", str49JsonObject.get("bankname"));//开户网点名称
				DataPoolUtil.addData(requestMessage, "ACCT_NAME", str49JsonObject.get("accountname"));//账户名
				DataPoolUtil.addData(requestMessage, "ACCOUNT", str49JsonObject.get("accountno"));//账户号
				DataPoolUtil.addData(requestMessage, "BUSI_TYPE", str49JsonObject.get("paytype"));//支付类型
				DataPoolUtil.addData(requestMessage, "PAY_BANK", str49JsonObject.get("obankid"));//目标账户开户行代码
				DataPoolUtil.addData(requestMessage, "UNIT_NAME", str49JsonObject.get("obankname"));//目标账户开户网点名称
				DataPoolUtil.addData(requestMessage, "STR32", str49JsonObject.get("oaccountname"));//目标账户名
				DataPoolUtil.addData(requestMessage, "ACCOUNT_NO", str49JsonObject.get("oaccountno"));//目标账户号
				DataPoolUtil.addData(requestMessage, "REMARKS", str49JsonObject.get("note"));//备注
				
				//202411-新增电子印章验证-begin ***********************************************************
				//新增字段-支付指令单-一定有值 ,从49域获取base64编码字符串
				DataPoolUtil.addData(requestMessage, "STR47", str49JsonObject.get("instructionfile"));//支付指令单附件（base64编码的有电子签章的pdf文件，pdf文件电子印章验证）
				//新增字段-可能有值,目前暂时不用该信息
				DataPoolUtil.addData(requestMessage, "STR48", str49JsonObject.get("attachfile"));//附件（base64编码的pdf文件，例如拨付协执相关文件）

				//校验pdf文件电子签章等信息
				if(pdfValidate(context)){
					log.info("["+message_id+"]base64编码的有电子签章的pdf文件验证完成");
					DataPoolUtil.addData(requestMessage, "FLAG", "有效");	//验签状态，在新一代页面显示
				}else{
					log.error("["+message_id+"]base64编码的有电子签章的pdf文件验证失败，请检查！");
					DataPoolUtil.addData(requestMessage, "FLAG", "无效");	//验签状态，在新一代页面显示
					return;
				}
				//202411-新增电子印章验证-end ***********************************************************
			
				String txn_brh_id = "";
				String tran_dt =DataPoolUtil.getData(requestMessage,"TX_DATE")==null ? "":DataPoolUtil.getData(requestMessage,"TX_DATE").toString();//时间
				log.info("tran_dt=" + tran_dt);
				if(tran_dt.length()==0){
					setRspInfo(requestMessage,"0","支付指令时间为空");
					return;
				}
				if(tran_dt.length()!=19){
					setRspInfo(requestMessage,"0","支付指令时间格式异常");
					return;
				}
				String tran_sq = DataPoolUtil.getData(requestMessage,"SEQNO")==null ? "":DataPoolUtil.getData(requestMessage,"SEQNO").toString();//指令流水号
				log.info("tran_sq=" + tran_sq);
				if(tran_sq.length()==0){
					setRspInfo(requestMessage,"0","指令流水为空");
					return;
				}
				
				BigDecimal tran_at =new BigDecimal(DataPoolUtil.getData(requestMessage,"SETTL_AMT")==null ? "0":DataPoolUtil.getData(requestMessage,"SETTL_AMT").toString()) ;//结算金额
				log.info("tran_at=" + tran_at);
				if(tran_at.compareTo(new BigDecimal(0)) <=0){
					setRspInfo(requestMessage,"0","结算金额必须大于0");
					return;
				}
				
				String open_brh_id = DataPoolUtil.getData(requestMessage,"INST_NO")==null ? "":DataPoolUtil.getData(requestMessage,"INST_NO").toString();//开户行代码
				log.info("open_brh_id=" + open_brh_id);
				if(open_brh_id.length()==0){
					setRspInfo(requestMessage,"0","支付指令开户(人行)银行代码为空");
					return;
				}
				String open_brh_nm = DataPoolUtil.getData(requestMessage,"INST_NAME")==null ? "":DataPoolUtil.getData(requestMessage,"INST_NAME").toString();//开户网点名称
				log.info("open_brh_nm=" + open_brh_nm);
				String acct_nm = DataPoolUtil.getData(requestMessage,"ACCT_NAME")==null ? "":DataPoolUtil.getData(requestMessage,"ACCT_NAME").toString();//账户名
				log.info("acct_nm=" + acct_nm);
				if(acct_nm.length()==0){
					setRspInfo(requestMessage,"0","支付指令账户户名为空");
					return;
				}
				String cpab_acc_id = DataPoolUtil.getData(requestMessage,"ACCOUNT")==null ? "":DataPoolUtil.getData(requestMessage,"ACCOUNT").toString();//账户号
				log.info("cpab_acc_id=" + cpab_acc_id);
				if(cpab_acc_id.length()==0){
					setRspInfo(requestMessage,"0","支付指令账号为空");
					return;
				}
				String vch_type = DataPoolUtil.getData(requestMessage,"BUSI_TYPE")==null ? "":DataPoolUtil.getData(requestMessage,"BUSI_TYPE").toString();//支付类型
				log.info("vch_type=" + vch_type);
				if(vch_type.length()==0){
					setRspInfo(requestMessage,"0","支付指令支付类型为空");
					return;
				}
				String peer_brh_id = DataPoolUtil.getData(requestMessage,"PAY_BANK")==null ? "":DataPoolUtil.getData(requestMessage,"PAY_BANK").toString();//目标账户开户行代码
				log.info("peer_brh_id=" + peer_brh_id);
				String peer_brh_nm = DataPoolUtil.getData(requestMessage,"UNIT_NAME")==null ? "":DataPoolUtil.getData(requestMessage,"UNIT_NAME").toString();//目标账户开户网点名称
				log.info("peer_brh_nm=" + peer_brh_nm);
				String peer_acc_nm = DataPoolUtil.getData(requestMessage,"STR32")==null ? "":DataPoolUtil.getData(requestMessage,"STR32").toString();//目标账户名
				log.info("peer_acc_nm" + peer_acc_nm);
				String peer_acc_id = DataPoolUtil.getData(requestMessage,"ACCOUNT_NO")==null ? "":DataPoolUtil.getData(requestMessage,"ACCOUNT_NO").toString();//目标账户号
				log.info("peer_acc_id=" + peer_acc_id);
				if(vch_type.equals("2")){
					if(peer_brh_id.length()==0){
						setRspInfo(requestMessage,"0","支付指令支付类型为空2-跨行时，目标账户开户行代码为空");
						return;
					}
					if(peer_brh_nm.length()==0){
						setRspInfo(requestMessage,"0","支付指令支付类型为空2-跨行时，目标账户开户网点名称为空");
						return;
					}
					if(peer_acc_nm.length()==0){
						setRspInfo(requestMessage,"0","支付指令支付类型为空2-跨行时，目标账户名为空");
						return;
					}
					if(peer_acc_id.length()==0){
						setRspInfo(requestMessage,"0","支付指令支付类型为空2-跨行时，目标账户号为空");
						return;
					}
				}					
				
				String aptn_tx = DataPoolUtil.getData(requestMessage,"REMARKS")==null ? "":DataPoolUtil.getData(requestMessage,"REMARKS").toString();//备注
				log.info("aptn_tx=" + aptn_tx);
				
				HashMap<String,Object> hmp_acc = new HashMap<String,Object>();
				hmp_acc.put("merch_id",merch_id);
				hmp_acc.put("ope_cd",ope_cd);
				hmp_acc.put("cpab_acc_id",cpab_acc_id);
				log.info("hmp="+hmp_acc);
				Object[] result_accmsg = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.check_acc", hmp_acc);
				if (result_accmsg.length == 0){
					//监管账户信息不存，返回匹配失败
					setRspInfo(requestMessage,"0","支付指令账户号不存在，请联系银行业务人员进行核对!");
					return;
				}else{
					HashMap map = (HashMap) result_accmsg[0];
				
					String instno = map.get("pbc_brh_id").toString();
					String acctname = map.get("acct_nm").toString();
					txn_brh_id = map.get("open_brh_id").toString();
					if(!acct_nm.equals(acctname)){
						setRspInfo(requestMessage,"0","支付指令账户户名不匹配，请联系银行业务人员进行核对!");
						return;
					}else if(!open_brh_id.equals(instno)){
						setRspInfo(requestMessage,"0","账号不存在或账号验证指令开户银行代码与账号不匹配,请联系银行业务人员进行核对!");
						return;
					}
				}
				HashMap<String,Object> Map = new HashMap<String, Object>();
				Map.put("merch_id", merch_id);
				Map.put("ope_cd", ope_cd);
				Map.put("trans_type", "zzg");
				Map.put("tran_sq", tran_sq);
				Object[] map_tran_sq = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.select_tb_zjjg_tran_info", Map);
				if (map_tran_sq.length > 0){
					//监管账户信息不存，返回匹配失败
					setRspInfo(requestMessage,"0","支付指令已存在，请联系银行业务人员进行核对!");
					return;
				}
				SimpleDateFormat date = new SimpleDateFormat("yyyyMMdd");
				Map.put("tran_dt", date.format(new Date()));
				Map.put("open_brh_id", open_brh_id);
				Map.put("open_brh_nm", open_brh_nm);
				Map.put("cpab_acc_id", cpab_acc_id);
				Map.put("acct_nm", acct_nm);
				Map.put("tran_at", tran_at);
				Map.put("vch_type", vch_type);
				Map.put("tran_fg", "0");//指令状态	0-收到指令，待支付，1-已支付，2-未支付，3-退款
				Map.put("peer_brh_id", peer_brh_id);
				Map.put("peer_brh_nm", peer_brh_nm);
				Map.put("peer_acc_nm", peer_acc_nm);
				Map.put("peer_acc_id", peer_acc_id);
				Map.put("aptn_tx", aptn_tx);
				Map.put("tran_time", tran_dt);
				Map.put("txn_brh_id", txn_brh_id);
				String pdfValidateRes = null;
				if(DataPoolUtil.getData(requestMessage,"FLAG")!=null){
					pdfValidateRes = (String)DataPoolUtil.getData(requestMessage,"FLAG");
					Map.put("oth_msg4_tx", pdfValidateRes);//验签状态
				}
				String pdfCertInfo = null;
				if(DataPoolUtil.getData(requestMessage,"STR46")!=null){//证书信息入库
					pdfCertInfo = (String)DataPoolUtil.getData(requestMessage,"STR46");
					Map.put("oth_msg5_tx", pdfCertInfo);//pdf证书信息
				}
				log.info("往数据库tb_zjjg_tran_info记录验签状态:"+pdfValidateRes+",和pdf证书信息："+pdfCertInfo);
				
				
				ITransactionManager iTransactionManager=TransactionManagerFactory.getTransactionManager();
				try{
					iTransactionManager.begin(); //事务开始
					
					log.info("===========参数"+Map);
					//更新监管账户信息
					DatabaseExt.executeNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.add_tb_merch_ope_trans_info", Map);
					
					iTransactionManager.commit();//事务提交
					setRspInfo(requestMessage,"1","账户支付指令登记成功");
				}catch (Exception e) {
					log.error("系统执行发生异常：" + PrintLog.getTrace(e));
					iTransactionManager.rollback();    //失败事务回滚
					setRspInfo(requestMessage,"0","账户支付指令登记失败");	
					return;
				}
				retcode = "0";
			}else if(inter_code.equals("20004")){
				tran_type = "退款通知";
				log.info("***退款通知:"+ inter_code);
				/*//通用必备参数
				DataPoolUtil.addData(requestMessage, "TX_CODE", "811605");
				DataPoolUtil.addData(requestMessage, "CLI_SERIAL_NO", FjCommUtil.getTimeMillStr());//前端流水号
				DataPoolUtil.addData(requestMessage, "CHNL_CODE", "15");//渠道代码
				DataPoolUtil.addData(requestMessage, "OUTSYS_CODE", outsys_code);//外系统代码
				DataPoolUtil.addData(requestMessage, "AREA_CODE", send_inst);
				String xz_code =  String.valueOf(DataPoolUtil.getData(requestMessage,"WAY_TYPE"));
	    		log.info("险种:"+xz_code);
				if(NoneType.isNullOrEmpty(xz_code))
	    		//if("null".equals(xz_code)  )
				{
					log.info("***业务类型BusiType默认为:20001");
					DataPoolUtil.addData(requestMessage, "TX_TYPE", "20001");
				}else{
					DataPoolUtil.addData(requestMessage, "TX_TYPE", "20"+xz_code);
				}*/
				retcode = "0";
			}else if(inter_code.equals("20005")){
				tran_type = "对账";
				log.info("***对账:"+ inter_code);
				
				String seqno = DataPoolUtil.getData(requestMessage,"SEQNO")==null ? "":DataPoolUtil.getData(requestMessage,"SEQNO").toString();//指令流水号
				log.info("seqno=" + seqno);
				if(seqno.length()==0){
					log.info("***报文内容检查不合法***");
					setRspInfo(requestMessage,"9999","报文内容检查不合法");
					return;
				}
				String state = "";
				String respInfo = "执行成功";
				
				HashMap<String,Object> hmp = new HashMap<String,Object>();
				hmp.put("tran_sq", seqno);
				hmp.put("merch_id", merch_id);
				hmp.put("ope_cd", ope_cd);
				hmp.put("trans_type", "zzg");
				log.info("hmp="+hmp);
				Object[] result_data = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.xmspf.select_tb_zjjg_tran_info", hmp);
				if (result_data.length == 0){
					log.info("未找到指令流水号["+seqno+"]对应的指令状态");
					respInfo = "未找到指令流水号";
					state = "0";
				}else{
					HashMap map = new HashMap();
					map = (HashMap) result_data[0];
					String tran_fg = map.get("tran_fg").toString();
					if("0".equals(tran_fg)||"1".equals(tran_fg)){
						state = "1";
					} else if("2".equals(tran_fg)){
						state = "0";
						respInfo = "指令流水号未支付";
					} else if("3".equals(tran_fg)){
						state = "0";
						respInfo = "指令流水号已退款";
					} else {
						state = "0";
						respInfo = "指令流水状态异常";
					}
				}
					
				DataPoolUtil.addData(requestMessage, "FLAG1", state);//支付状态
				
				setRspInfo(requestMessage,"1", respInfo);
				
			}	
		} catch (Exception e) {
			log.error("["+message_id+"] Beforesend异常，请核实出错定位：" + PrintLog.getTrace(e));
			// 如果出现问题 添加错误提示
			setRspInfo(requestMessage,"0","交易失败");
			return;
		} finally {
			HashMap<String, String> paramMap = new HashMap<>();
			String out_sys_code = GetProperties.sysID;
			String merch_id = GetProperties.merch_id;
			String ope_cd = GetProperties.ope_cd;
			String pay_type_cd = (String) DataPoolUtil.getData(requestMessage,"INTER_CODE");
			String tran_dt = (String) DataPoolUtil.getData(requestMessage,"TX_DATE");
			String pay_id = (String) DataPoolUtil.getData(requestMessage,"SEQNO");
			String tran_sq = new SimpleDateFormat("HHmmssSSS").format(new Date());
			String respInfo = (String) DataPoolUtil.getData(requestMessage,"STR50");
			String respCode = (String) DataPoolUtil.getData(requestMessage,"STATE");
			paramMap.put("out_sys_code", out_sys_code);
			paramMap.put("merch_id", merch_id);
			paramMap.put("ope_cd", ope_cd);
			paramMap.put("tran_sq", tran_sq.substring(0, 8));
			paramMap.put("tran_inst_id", "999999");
			paramMap.put("tran_cd", "999999");
			paramMap.put("pay_type_cd", pay_type_cd);
			paramMap.put("tran_dt", tran_dt == null ? new SimpleDateFormat("yyyyMMdd").format(new Date()) : tran_dt.replace("-", "").substring(0, 8));
			paramMap.put("tlr_id", "999999");
			paramMap.put("pay_id", pay_id);
			paramMap.put("oth_msg1_tx", respCode + "|" + respInfo);
			paramMap.put("tran_stat_cd", respCode);
			paramMap.put("oth_msg2_tx", message_id);
			paramMap.put("oth_pr_msg3", tran_type);
			if(DataPoolUtil.getData(requestMessage,"STR46")!=null){//证书信息入库
				//String pdfCertInfo = (String)DataPoolUtil.getData(requestMessage,"STR46");
				paramMap.put("oth_msg8_tx", (String) DataPoolUtil.getData(requestMessage,"STR46"));
			}
			if(DataPoolUtil.getData(requestMessage,"FLAG")!=null){
				String pdfValidateRes = (String)DataPoolUtil.getData(requestMessage,"FLAG");
				paramMap.put("oth_msg3_tx", pdfValidateRes);
			}
			log.info("===========日志参数："+paramMap);
			log.info(LogTableDAO.insertLogRecord(paramMap));//记录日志，含验签状态
			log.info("["+message_id+"]Beforesend处理完成");
		}
	}

	
	/**
	 * 通用调用后台，覆盖返回值
	 * @param context
	 * @return
	 */
	@Bizlet("通用调用后台")
	public static void CommonHost(IKernelServiceContext context) {
		//获取容器
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		log.debug("=========通用调用后台=========");
		DataPoolUtil.addData(requestMessage, "INTER_CODE", "OSTS001");
		HostServiceInvoker.invoke(context);
	}
	
	@Bizlet
	public void errorCheck(IKernelServiceContext context){
		//获取容器
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		DataPoolUtil.addData(requestMessage, "RET_CODE", "9999");
		DataPoolUtil.addData(requestMessage, "RET_EXPLAIN", "系统错误");
	}
	
	/**
	 * base64编码的有电子签章的pdf文件验证
	 * */
	public static boolean pdfValidate(IKernelServiceContext context){
		/* Map<String,Object> map = null;
		 map.put("code", "0");
		 map.put("msg", "默认校验不通过");*/
		//获取容器
		Message<?> requestMessage = context.getServiceRequest().getRequestMessage();
		String message_id = (String) requestMessage.getHeaders().get("$btp.message.id");
		
		String pdfPath = GetProperties.pdfPath;//存放base64文件的路径
	    String cerPath = GetProperties.cerPath;//存放证书文件的全路径
	    FileInputStream fis = null;
		
		
		//时间戳，用于文件名生成
		SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
		String timeStr =  df.format(new Date());
		
		String base64Pdf = DataPoolUtil.getData(requestMessage, "STR47") == null?"":DataPoolUtil.getData(requestMessage, "STR47").toString();
	//	log.info("["+message_id+"]base64编码的有电子签章的pdf文件：" + base64Pdf);
		if(base64Pdf!=null){
			log.info("["+message_id+"]base64编码的有电子签章的pdf文件：");
		}
		
	//	log.info("["+message_id+"]支付类型："+DataPoolUtil.getData(requestMessage, "BUSI_TYPE") == null?"":DataPoolUtil.getData(requestMessage, "BUSI_TYPE").toString()+"，  base64编码的pdf文件，例如拨付协执相关文件："+ DataPoolUtil.getData(requestMessage, "STR48") == null?"":DataPoolUtil.getData(requestMessage, "STR48").toString());
		
	 //   base64Pdf = TestSign.encryptToBase64(pdfPath+"new.pdf");//测试数据
	 //   System.out.print("["+message_id+"]测试pdf的base64编码---"+base64Pdf);
	    String pdffilePath = null;
		if(base64Pdf!=null&&base64Pdf!=""){
			//解析base64成pdf文件
			pdffilePath = pdfPath+"pdf20003_"+timeStr+".pdf";
			if (base64Pdf == null && pdffilePath == null) {
				log.error("["+message_id+"]生成文件"+pdffilePath+"失败，请给出相应的数据(base64Pdf缺失)。");
				setRspInfo(requestMessage,"0","生成文件失败，请给出相应的数据");
				 return false;
	        }
	       try {
	            Files.write(Paths.get(pdffilePath), Base64.getDecoder().decode(base64Pdf),StandardOpenOption.CREATE);
	            log.info("["+message_id+"]生成文件完成");
	       } catch (IOException e) {
	           e.printStackTrace();
	       }
		}
		
		 // 创建 PdfDocument 对象
        PdfDocument pdf = new PdfDocument();
      
        // 加载 PDF 文档
        pdf.loadFromFile(pdffilePath);

        // 获取 PDF 文档的表单
        PdfFormWidget formWidget = (PdfFormWidget) pdf.getForm();

        if (formWidget.getFieldsWidget().getCount() > 0) {
            // 遍历表单中的所有域
            for (int i = 0; i < formWidget.getFieldsWidget().getCount(); i++) {
                PdfField field = formWidget.getFieldsWidget().get(i);
                // 查找签名域
                if (field instanceof PdfSignatureFieldWidget) {
                    PdfSignatureFieldWidget signatureField = (PdfSignatureFieldWidget) field;
                    // 获取签名
                    PdfSignature signature = signatureField.getSignature();

                    // 验证签名是否被篡改
                    boolean modified = signature.verifyDocModified();
                    if (modified) {
                    	setRspInfo(requestMessage,"0","pdf文件被篡改！");
                        log.info("["+message_id+"]pdf文件被篡改！");
                        return false;
                    } 

                    //获取getCertificate里的签名信息
                    log.info("["+message_id+"]pdf中的证书信息："+signature.getCertificate());
                    String cerInfo = signature.getCertificate().toString();//拼接证书信息
                  //  DataPoolUtil.addData(requestMessage, "STR46",signature.getCertificate());//pdf中的证书信息
                    DataPoolUtil.addData(requestMessage, "STR46",cerInfo);//pdf中的证书信息
                    
            		
                    Date wdate = signature.getDate();
                    try {

                    	// 创建一个证书工厂
                    	CertificateFactory factory = CertificateFactory.getInstance("X.509");
                    	// 读取证书链文件
                    	fis = new FileInputStream(cerPath);
                     	//将证书链读取为X509Certificate对象的集合
                    	Collection<? extends X509Certificate> certs = (Collection<? extends X509Certificate>) factory.generateCertificates(fis);
                    	log.info("["+message_id+"]cfca证书信息:"+certs.toString());
                        Date beginDate = null;
                        Date endDate = null;
                        // 遍历并打印每个证书的信息:主题、颁发者、序列号、有效期等
                     
                        for (X509Certificate cert : certs) {
                            log.info("["+message_id+"]Subject DN: " + cert.getSubjectDN());
                            String[] array = cert.getSubjectDN().toString().split(",");
                            Map<String,Object> map = new HashMap<>();
                            for(String a:array){
                                String[] k = a.split("=");
                                String key = k[0];
                                String value = k[1];
                                map.put(key,value);
                            }
                            log.info("证书使用者属性:"+map.toString());
                            log.info("文件名："+map.get("CN"));
                            if(map.get("CN")!=null){
                            	
                            }else{
                            	 setRspInfo(requestMessage,"0","印章证书签发机构无效");
                            	 log.info("["+message_id+"]印章证书签发机构无效，文件名为空值");
                            	 return false;
                            }
                            String certname = map.get("CN").toString();
                            if(!(certname.equals("CFCA ACS OCA31")||certname.equals("CFCA ACS TEST OCA31"))){
                          
                                //名称不一致
                                setRspInfo(requestMessage,"0","印章证书签发机构无效");
                                log.info("["+message_id+"]印章证书签发机构无效，证书名称："+certname);
                                return false;
                            }


                            SimpleDateFormat sdf = new SimpleDateFormat("YYYY-MM-dd HH:mm:ss");
                            log.info("["+message_id+"]电子印章签署日期: " +sdf.format(wdate));
                            wdate = sdf.parse(sdf.format(wdate));
                            log.info("["+message_id+"]证书颁发日期: " + sdf.format(cert.getNotBefore()));
                            beginDate = sdf.parse(sdf.format(cert.getNotBefore()));
                            log.info("["+message_id+"]证书截止日期: " + sdf.format(cert.getNotAfter()));
                            endDate =sdf.parse(sdf.format(cert.getNotAfter()));
                            Boolean before = wdate.before(endDate);
                            Boolean after = wdate.after(beginDate);
                            log.info("签署日期在颁发日期后："+after);
                            log.info("签署日期在截止日期前："+before);
                            if(!(before&&after)){
                            	setRspInfo(requestMessage,"0","持证人（使用者）证件号码不正确");
                                log.error("["+message_id+"]盖章时间不在证书有效范围内");
                                return false;
                            }
                            
                            //验证证书持证人（使用者）名称和证件号码。名称：厦门市住房和建设局，证件号码：11350200004138453G。
                            //提取方式从证书使用者属性CN字段截取。报错信息：持证人（使用者）名称或证件号码不正确。
                            String validate4str = signature.getCertificate().getSubject().toString();
                            String[] v1 = validate4str.split(",");
                            Map<String,Object> map4 = new HashMap<>();
                            for(String v :v1){
                                String[] v2 = v.split("=");
                                String key = v2[0];
                                String value = v2[1];
                                map4.put(key,value);
                            }
                        	String v3  = null;
                            if(map4.get("CN")!=null){
                                v3= map4.get("CN").toString();
                                log.info("["+message_id+"]证书使用者属性CN字段---------"+v3);
                                String[] v3arr = v3.split("@");
                                for(int j =0;j<v3arr.length;j++){
                                    log.info("["+message_id+"]解析CN数组，v3arr["+j+"]="+v3arr[j]);
                                }
                                log.info(v3arr[1]+"和配置的持证人名称："+GetProperties.userName);
                                if(!v3arr[1].equals(GetProperties.userName)){//厦门市住房和建设局
                                	setRspInfo(requestMessage,"0","持证人（使用者）名称不正确");
                                    log.error("["+message_id+"]持证人（使用者）名称不正确");
                                    return false;
                                }
                                log.info(v3arr[2]+"配置的持证人（使用者）证件号码："+GetProperties.userCardno);
                                if(!v3arr[2].equals(GetProperties.userCardno)){
                                	setRspInfo(requestMessage,"0","持证人（使用者）证件号码不正确");
                                    log.error("["+message_id+"]持证人（使用者）证件号码不正确");
                                    return false;
                                }
                            }
                            
                        }
                        
                    } catch (Exception e) {
                        e.printStackTrace();
                    }finally{
                    	try {
                    		if(fis!=null){
								fis.close();
                    		}
                    	} catch (IOException e) {
							// TODO 自动生成的 catch 块
							e.printStackTrace();
						}
                    }
                }
            }
        }
       
		return true;

	}
	
	
	//************************以下方法测试用，升级需要删除
	/**
     * 文件转base64
     * */
    public static String encryptToBase64(String filePath) {
        if (filePath == null) {
             return null;
           }
     try {
          byte[] b = Files.readAllBytes(Paths.get(filePath));
          return Base64.getEncoder().encodeToString(b);
     } catch (IOException e) {
           e.printStackTrace();
     }
        return null;
  }
    /**
     * base64转文件
     * */
    public static String decryptByBase64(String base64, String filePath) {
        if (base64 == null && filePath == null) {
              return "生成文件失败，请给出相应的数据。";
        }
       try {
            Files.write(Paths.get(filePath), Base64.getDecoder().decode(base64),StandardOpenOption.CREATE);
       } catch (IOException e) {
           e.printStackTrace();
       }
       return "指定路径下生成文件成功！";
    }
}







