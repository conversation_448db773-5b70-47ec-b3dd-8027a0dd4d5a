<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_common.XMWEB_COMMON_0003RES" xmlns:common_head_res="prov3502_common.common_head_res" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_common.XMWEB_COMMON_0003RES">
    <xs:import namespace="prov3502_common.common_head_res" schemaLocation="../prov3502_common/common_head_res_msg_btx.xsd"/>
    <xs:complexType name="list">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="batch_id" nillable="true" type="xs:string"/>
            <xs:element name="plan_tran_dt" nillable="true" type="xs:string"/>
            <xs:element name="exec_dt" nillable="true" type="xs:string"/>
            <xs:element name="total_qt" nillable="true" type="xs:string"/>
            <xs:element name="total_at" nillable="true" type="xs:string"/>
            <xs:element name="proc_fg" nillable="true" type="xs:string"/>
            <xs:element name="tran_dt" nillable="true" type="xs:string"/>
            <xs:element name="req_file_name_tx" nillable="true" type="xs:string"/>
            <xs:element name="rsp_file_name" nillable="true" type="xs:string"/>
            <xs:element name="core_batch_id" nillable="true" type="xs:string"/>
            <xs:element name="succ_qt" nillable="true" type="xs:string"/>
            <xs:element name="succ_at" nillable="true" type="xs:string"/>
            <xs:element name="fail_qt" nillable="true" type="xs:string"/>
            <xs:element name="fail_at" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="list" nillable="true" type="list"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="XMWEB_COMMON_0003RES">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="common_head_res:common_head_res"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
