<map>
  <entry>
    <string>com.psbc.pfpj.prov3502.BeforSend.errorCheck(com.primeton.btp.api.engine.context.IKernelServiceContext)</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;void com.psbc.pfpj.prov3502.BeforSend.errorCheck(IKernelServiceContext context)&lt;/b&gt;&lt;br/&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
  <entry>
    <string>com.psbc.pfpj.prov3502.BeforSend.SetData(com.primeton.btp.api.engine.context.IKernelServiceContext)</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;void com.psbc.pfpj.prov3502.BeforSend.SetData(IKernelServiceContext context)&lt;/b&gt;&lt;br/&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
  <entry>
    <string>BeforSend [厦门商品房]</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;com.psbc.pfpj.prov3502.BeforSend&lt;/b&gt;&lt;br/&gt;&lt;p&gt; &lt;dl&gt;&lt;dt&gt;作者:&lt;/dt&gt;&lt;dd&gt;hyq&lt;/dd&gt;&lt;dt&gt;@date&lt;/dt&gt;&lt;dd&gt;2020-8-24 09:12:33&lt;/dd&gt;&lt;/dl&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
  <entry>
    <string>com.psbc.pfpj.prov3502.BeforSend.CommonHost(com.primeton.btp.api.engine.context.IKernelServiceContext)</string>
    <string>&lt;html&gt;&lt;head&gt;&lt;/head&gt;&lt;body&gt;&lt;b&gt;void com.psbc.pfpj.prov3502.BeforSend.CommonHost(IKernelServiceContext context)&lt;/b&gt;&lt;br/&gt;&lt;p&gt; 通用调用后台，覆盖返回值&#x0D;
 &lt;dl&gt;&lt;dt&gt;参数：&lt;/dt&gt;&lt;dd&gt;&lt;b&gt;context&lt;/b&gt;&lt;/dd&gt;&lt;dt&gt;返回：&lt;/dt&gt;&lt;dd&gt;&lt;/dd&gt;&lt;/dl&gt;&lt;/font&gt;&lt;/body&gt;&lt;/html&gt;</string>
  </entry>
</map>