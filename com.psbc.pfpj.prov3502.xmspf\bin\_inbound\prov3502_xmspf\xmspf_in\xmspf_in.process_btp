<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="xmspf_in.process_btx" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******">
  <nodes xsi:type="process:tStart" id="start0" name="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokePojo0</targetNode>
    </sourceConnections>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" type="end">
    <targetConnections>link3</targetConnections>
    <targetConnections>link6</targetConnections>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" language="prov3502_xmspf.xmspf_inRES.xmspf_inRES" name="outMessage" type="query" valueType="DataObject">outMessage</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" nodeType="label">
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" nodeType="label">
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" id="invokePojo0" name="支付指令查询/支付指令状态维护本地处理、退款通知前处理" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link4" name="link4" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>switch0</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query"/>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <sourceConnections xsi:type="process:tLink" id="link7" name="link7" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>invokePojo1</targetNode>
      <process:transitionCondition>
        <process:simpleCondition operator="EQ">
          <process:leftOperand type="query">retcode</process:leftOperand>
          <process:rightOperand type="literal">1</process:rightOperand>
        </process:simpleCondition>
      </process:transitionCondition>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="static" synchronization="true" transactionType="join">
      <process:partner type="literal">com.psbc.pfpj.prov3502.XmspfPaydeal.PaydealPub</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="context" type="query" value="com.primeton.btp.api.engine.context.IKernelServiceContext" valueType="Java" pattern="reference">__kernel_service_context</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables>
      <process:outputVariable id="0" name="out0" type="query" value="java.lang.String" valueType="Java">retcode</process:outputVariable>
    </process:outputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <figSize height="17" width="318"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" id="invokePojo2" name="退款通知后处理" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" isDefault="true" type="transition">
      <sourceNode>invokePojo2</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link8</targetConnections>
    <nodeLabel>invokePojo2label</nodeLabel>
    <process:pojo methodType="static" synchronization="true" transactionType="join">
      <process:partner type="literal">com.psbc.pfpj.prov3502.XmspfPaydeal.PaydealRet</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="context" type="query" value="com.primeton.btp.api.engine.context.IKernelServiceContext" valueType="Java" pattern="reference">__kernel_service_context</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo2label" name="label" nodeType="label">
    <figSize height="17" width="85"/>
    <node>invokePojo2</node>
  </nodes>
  <nodes xsi:type="process:tSwitch" id="switch0" name="空操作" type="switch">
    <sourceConnections xsi:type="process:tLink" id="link5" name="link5" isDefault="true" type="transition">
      <sourceNode>switch0</sourceNode>
      <targetNode>switch1</targetNode>
    </sourceConnections>
    <targetConnections>link4</targetConnections>
    <nodeLabel>switch0label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="switch0label" name="label" nodeType="label">
    <figSize height="17" width="37"/>
    <node>switch0</node>
  </nodes>
  <nodes xsi:type="process:tSwitch" id="switch1" name="空操作1" type="switch">
    <sourceConnections xsi:type="process:tLink" id="link6" name="link6" isDefault="true" type="transition">
      <sourceNode>switch1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link5</targetConnections>
    <nodeLabel>switch1label</nodeLabel>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="switch1label" name="label" nodeType="label">
    <figSize height="17" width="44"/>
    <node>switch1</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" id="invokePojo1" name="调用委托方20004退款通知接口" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link8" name="link8" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>invokePojo2</targetNode>
    </sourceConnections>
    <targetConnections>link7</targetConnections>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.psbc.pfpj.prov3502.HostServiceInvoker.invoke</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="context" type="query" value="com.primeton.btp.api.engine.context.IKernelServiceContext" valueType="Java" pattern="reference">__kernel_service_context</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <figSize height="17" width="168"/>
    <node>invokePojo1</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info version="*******"/>
  <process:variables>
    <process:variable historyStateLocation="client" isArray="false" name="retcode" primitiveType="String"/>
  </process:variables>
  <process:inputs>
    <process:input anyType="com.primeton.btp.api.engine.context.IKernelServiceContext" isArray="false" name="__kernel_service_context"/>
    <process:input isArray="false" modelType="prov3502_xmspf.xmspf_inREQ.xmspf_inREQ" name="inMessage"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" modelType="prov3502_xmspf.xmspf_inRES.xmspf_inRES" name="outMessage"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
</process:tBusinessLogic>
