<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="Administrator" category="message" create-date="2021-11-05 15:57:03" version="7.0.0.0">
  <message-definition bitmap-format="Binary" bitmap-length="0" bitmap-type="unionpay" category="xml" filter-null="false" id="Hxmspf_20002REQ" name="请求报文" namespace="prov35_xmspf.Hxmspf_20002REQ" orderly="false" xml-pack-simple-node="false" xml-pack-statement="true" nodef-json="false">
    <group-message-item display-name="content" name="content" seqno="0" xml-prefix="">
      <group-message-item display-name="head" name="head" seqno="0" xml-prefix="">
        <group-message-item display-name="serviceno" name="serviceno" seqno="0" xml-prefix="">
          <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="TRAN_TYPE" display-name="服务编号" field-type="transCode" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="服务编号" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
            <ext-property/>
            <description></description>
          </message-item>
          <ext-property/>
        </group-message-item>
        <group-message-item display-name="usr" name="usr" seqno="1" xml-prefix="">
          <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="OPR_NAME" display-name="用户名" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="用户名" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
            <ext-property/>
            <description></description>
          </message-item>
          <ext-property/>
        </group-message-item>
        <group-message-item display-name="pwd" name="pwd" seqno="2" xml-prefix="">
          <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="PASSWORD" display-name="用户密码" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="用户密码" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
            <ext-property/>
            <description></description>
          </message-item>
          <ext-property/>
        </group-message-item>
        <group-message-item display-name="optname" name="optname" seqno="3" xml-prefix="">
          <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="TLR_NAME" display-name="实际操作人" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="实际操作人" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
            <ext-property/>
            <description></description>
          </message-item>
          <ext-property/>
        </group-message-item>
        <group-message-item display-name="signmsg" name="signmsg" seqno="4" xml-prefix="">
          <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR31" display-name="签名信息" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="签名信息" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
            <ext-property/>
            <description></description>
          </message-item>
          <ext-property/>
        </group-message-item>
        <ext-property/>
      </group-message-item>
      <group-message-item display-name="body" name="body" seqno="1" xml-prefix="">
        <group-message-item display-name="table_account" name="table_account" seqno="0" xml-prefix="">
          <repeated-item display-name="row" name="row" out-key="true" repeat-field="" repeat-times="-1" seqno="0">
            <group-message-item display-name="optdate" name="optdate" seqno="0" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="TX_DATE" display-name="记账日期时间" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="记账日期时间" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
            <group-message-item display-name="detailno" name="detailno" seqno="1" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="SEQNO" display-name="明细流水号" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="明细流水号" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
            <group-message-item display-name="bankid" name="bankid" seqno="2" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="BANK_NO" display-name="开户银行代码" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="开户银行代码" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
            <group-message-item display-name="bankname" name="bankname" seqno="3" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="OPEN_UNIT_NAME" display-name="开户网点名称" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="开户网点名称" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
            <group-message-item display-name="accountname" name="accountname" seqno="4" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="ACCT_NAME" display-name="账户名" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="账户名" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
            <group-message-item display-name="accountno" name="accountno" seqno="5" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR16" display-name="账户号" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="账户号" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
            <group-message-item display-name="amount" name="amount" seqno="6" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR41" display-name="交易金额" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="交易金额" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
            <group-message-item display-name="amounttype" name="amounttype" seqno="7" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="AMNT_TYPE" display-name="金额变动类型" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="金额变动类型" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
            <group-message-item display-name="entrysourcetype" name="entrysourcetype" seqno="8" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR20" display-name="入账来源方式" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="入账来源方式" pad-char="0x20" seqno="0" validate-rule="" value="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
            <group-message-item display-name="balance" name="balance" seqno="9" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR42" display-name="账户余额" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="账户余额" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
            <group-message-item display-name="obankid" name="obankid" seqno="10" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="PAY_BANK" display-name="对方账户银行" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="对方账户银行" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
            <group-message-item display-name="obankname" name="obankname" seqno="11" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR33" display-name="对方账户开户网点名称" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="对方账户开户网点名称" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
            <group-message-item display-name="oaccountname" name="oaccountname" seqno="12" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR32" display-name="对方账户名" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="对方账户名" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
            <group-message-item display-name="oaccountno" name="oaccountno" seqno="13" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR17" display-name="对方账户号" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="对方账户号" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
            <group-message-item display-name="note" name="note" seqno="14" xml-prefix="">
              <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="REMARKS" display-name="备注" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="备注" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
                <ext-property/>
                <description></description>
              </message-item>
              <ext-property/>
            </group-message-item>
          </repeated-item>
          <ext-property/>
        </group-message-item>
        <ext-property/>
      </group-message-item>
      <ext-property/>
    </group-message-item>
  </message-definition>
</configuration>