<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov35_xmspf.Hxmspf_20004RES" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xmspf_msghead_res="xmspf.xmspf_msghead_res" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov35_xmspf.Hxmspf_20004RES">
    <xs:import namespace="xmspf.xmspf_msghead_res" schemaLocation="../xmspf/xmspf_msghead_res_msg_btx.xsd"/>
    <xs:complexType name="instructionno">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="指令流水号" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="issuccess">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="是否成功" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="row">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="instructionno" nillable="true" type="instructionno"/>
            <xs:element name="issuccess" nillable="true" type="issuccess"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="table_account">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="row" nillable="true" type="row"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="table_account" nillable="true" type="table_account"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="content">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="xmspf_msghead_res:xmspf_msghead_res"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Hxmspf_20004RES">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="content" nillable="true" type="content"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
