<?xml version="1.0" encoding="UTF-8"?>
<process:tBusinessLogic xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:model="http://com.primeton.emf.core" xmlns:process="http://primeton.com/emf/core/process" name="XMWEB_COMMON_0001.process_btx" gridVisibility="false" rulerVisibility="true" snapToGeometry="true" productVersion="*******">
  <nodes xsi:type="process:tStart" id="start0" name="开始" type="start">
    <sourceConnections xsi:type="process:tLink" id="link3" name="link3" isDefault="true" type="transition">
      <sourceNode>start0</sourceNode>
      <targetNode>invokePojo0</targetNode>
    </sourceConnections>
    <nodeLabel>start0label</nodeLabel>
  </nodes>
  <nodes xsi:type="process:tEnd" id="end0" name="结束" type="end">
    <targetConnections>link1</targetConnections>
    <nodeLabel>end0label</nodeLabel>
    <process:returns>
      <process:return id="0" name="outMessage" type="query">outMessage</process:return>
    </process:returns>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="start0label" name="label" nodeType="label">
    <figSize height="17" width="25"/>
    <node>start0</node>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="end0label" name="label" nodeType="label">
    <figSize height="17" width="25"/>
    <node>end0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" id="invokePojo0" name="运算逻辑" type="invoke" index="0" invokeType="invoke_pojo" varArgs="false">
    <sourceConnections xsi:type="process:tLink" id="link0" name="link0" isDefault="true" type="transition">
      <sourceNode>invokePojo0</sourceNode>
      <targetNode>invokePojo1</targetNode>
    </sourceConnections>
    <targetConnections>link3</targetConnections>
    <nodeLabel>invokePojo0label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner type="literal">com.psbc.pfpj.prov3502.xmweb.xmwebcommonKey.xmwebcommonKey</process:partner>
      <process:instance instanceName=""/>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="context" type="query" value="com.primeton.btp.api.engine.context.IKernelServiceContext" valueType="Java" pattern="reference">__kernel_service_context</process:inputVariable>
    </process:inputVariables>
    <process:outputVariables/>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo0label" name="label" nodeType="label">
    <figSize height="17" width="49"/>
    <node>invokePojo0</node>
  </nodes>
  <nodes xsi:type="process:tInvoke" id="invokePojo1" name="主机服务" collapsed="false" type="invoke" invokeType="invoke_hostService">
    <sourceConnections xsi:type="process:tLink" id="link1" name="link1" isDefault="true" type="transition">
      <sourceNode>invokePojo1</sourceNode>
      <targetNode>end0</targetNode>
    </sourceConnections>
    <targetConnections>link0</targetConnections>
    <nodeLabel>invokePojo1label</nodeLabel>
    <process:pojo methodType="instance" synchronization="true" transactionType="join">
      <process:partner>com.primeton.btp.impl.engine.processflow.hosttrans.HostTransProcessStep.execute</process:partner>
      <process:instance>
        <process:constructArgs>
          <process:arg>prov3502.HOSTS001RES.HOSTS001RES</process:arg>
        </process:constructArgs>
      </process:instance>
    </process:pojo>
    <process:inputVariables>
      <process:inputVariable id="0" name="bizContext" type="expression" value="com.primeton.ext.engine.core.IRuntimeContext" valueType="Java" pattern="reference">__context</process:inputVariable>
      <process:inputVariable id="1" name="instance" type="expression" value="com.eos.engine.core.bizlogic.IBizlogicInstance" valueType="Java" pattern="reference">this</process:inputVariable>
      <process:inputVariable id="2" name="kernelServiceContext" type="query" value="com.primeton.btp.api.engine.context.IKernelServiceContext" valueType="Java" pattern="reference">__kernel_service_context</process:inputVariable>
      <process:inputVariable id="3" name="hostServiceCode" type="literal" value="java.lang.String" valueType="Java" pattern="reference">HOSTS001</process:inputVariable>
      <process:inputVariable id="4" name="callType" type="literal" value="java.lang.String" valueType="Java" pattern="reference">synchronous</process:inputVariable>
      <process:inputVariable id="5" name="param" type="query" value="java.io.Serializable" valueType="Java" pattern="reference">inMessage</process:inputVariable>
      <process:inputVariable id="6" name="returnValue" type="literal" value="java.lang.String" valueType="Java" pattern="reference">outMessage</process:inputVariable>
    </process:inputVariables>
  </nodes>
  <nodes xsi:type="model:NodeElementLabel" id="invokePojo1label" name="label" nodeType="label">
    <figSize height="17" width="49"/>
    <node>invokePojo1</node>
  </nodes>
  <topRuler/>
  <leftRuler/>
  <process:info version="*******"/>
  <process:inputs>
    <process:input anyType="com.primeton.btp.api.engine.context.IKernelServiceContext" isArray="false" name="__kernel_service_context"/>
    <process:input isArray="false" modelType="prov3502_common.XMWEB_COMMON_0001REQ.XMWEB_COMMON_0001REQ" name="inMessage"/>
  </process:inputs>
  <process:outputs>
    <process:output description="" isArray="false" modelType="prov3502_common.XMWEB_COMMON_0001RES.XMWEB_COMMON_0001RES" name="outMessage"/>
  </process:outputs>
  <process:extAttribute1>public</process:extAttribute1>
</process:tBusinessLogic>
