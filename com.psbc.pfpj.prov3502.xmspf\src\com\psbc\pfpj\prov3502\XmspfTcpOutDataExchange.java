/*package com.psbc.pfpj.prov3502;

import java.io.UnsupportedEncodingException;

import org.apache.commons.lang.StringUtils;

import com.primeton.btp.api.core.exception.BTPRuntimeException;
import com.primeton.btp.api.core.logger.ILogger;
import com.primeton.btp.api.core.logger.LoggerFactory;
import com.primeton.btp.api.core.utils.ExtPropertyHelper;
import com.primeton.btp.api.endpoint.IEndpoint;
import com.primeton.btp.api.tcp.netty.buffer.ChannelBuffer;
import com.primeton.btp.api.tcp.netty.buffer.ChannelBuffers;
import com.primeton.btp.api.tcp.netty.channel.Channel;
import com.primeton.btp.api.tcp.netty.channel.ChannelHandlerContext;
import com.primeton.btp.api.transport.tcp.ITcpTransport;
import com.primeton.btp.api.transport.tcp.exception.ExceptionCodes;
import com.primeton.btp.spi.endpoint.tcp.ITcpDataExchange;
import com.primeton.btp.spi.transport.tcp.AbstractTcpTransport;

public class XmspfTcpOutDataExchange implements ITcpDataExchange {
	private ILogger log = LoggerFactory.getLogger(XmspfTcpOutDataExchange.class);
	private IEndpoint endpoint;
    
	protected int TIME_LEN = 1350;
	protected int headBytesLength = 6;

	protected boolean lengthContainsHeadBytes = true;

	public static final int MAX_HEAD_BYTES_LENGTH = 6;

	public static final int DEFAULT_HEAD_BYTES_LENGTH = 6;

	public void setEndpoint(IEndpoint endpoint) {
		this.endpoint = endpoint;
	}
	
	public IEndpoint getEndpoint() {
		return this.endpoint;
	}

	public byte[] read(ChannelBuffer buffer) {
		return read(null, null, buffer, null);
	}
	
	public byte[] read(ChannelHandlerContext ctx, Channel channel, ChannelBuffer buffer,
			AbstractTcpTransport transport) {

		log.info("收到厦门商品房服务端返回的应答");
		
		log.info(buffer.readableBytes()+":"+this.headBytesLength);
		
		int receiveLength = buffer.readableBytes();	
		
		log.error("接收到的密文长度:"+receiveLength);	
		if (receiveLength <= 0 || receiveLength==1024) {
			return null;
		}
		
		byte[] body = new byte[receiveLength];
		buffer.readBytes(body);//获取接收报文
		
		//log.error("接收到的密文(16进制):"+ByteArrayUtil.toHexString(body));	
		//log.error("实际接收到的密文长度(字节):"+body.length);	

		RsMessage rsm = new RsMessage();
		body = rsm.RsDecrypt(body);
		if(NoneType.isNull(body)){
			log.error("-------------------RsDecryptFail:报文解签解密失败--------------------");
			return null;
		}
		
		String xmlStr = null;
		try {
			xmlStr = new String(body,"UTF-8");
			xmlStr = xmlStr.substring(4, xmlStr.length());
			//xmlStr = xmlStr.replaceFirst("<InstId>","<InstId1>").replaceFirst("<\\/InstId>", "</InstId1>").replaceFirst("<InstId>","<InstId2>").replaceFirst("<\\/InstId>","</InstId2>");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		log.info("*****收到的报文内容：:" + xmlStr);	
		return xmlStr.getBytes();
	}

	public ChannelBuffer write(byte[] bytes) {

		String orgSendData = new String(bytes);
		
		orgSendData = orgSendData.replaceAll("InstId1","InstId").replaceAll("InstId2","InstId");
		log.info("*****发送报文内容:" + orgSendData);
		
		int dataLength = 0;
		try {
			dataLength = orgSendData.getBytes("UTF-8").length;
		} catch (UnsupportedEncodingException e) {
			// TODO 自动生成的 catch 块
			e.printStackTrace();
		}
		String lenStr = String.format("%04d", dataLength);
		log.info("向发送给委托方的报文添加长度："+lenStr);
		
		String newSendData =lenStr+orgSendData;
		log.info("拼接后报文:"+newSendData);
		

		bytes=null;
		bytes= newSendData.getBytes();
		
		
		RsMessage rsm = new RsMessage();
		bytes=null;
		bytes= rsm.RsEncrypt(newSendData.getBytes());
		if(NoneType.isNull(bytes)){
			log.error("-------------------RsEncryptFail:报文加加密失败--------------------");
			return null;
		}
		ChannelBuffer sendBuffer = null;
		sendBuffer = ChannelBuffers.buffer(bytes.length);//length=15
		sendBuffer.writeBytes(bytes);
		return sendBuffer;
		
	}
    public static void main(String[] args) {
		String orgSendData="厦门商品房123";
		System.out.println("长度："+orgSendData.length());
	}

}
*/