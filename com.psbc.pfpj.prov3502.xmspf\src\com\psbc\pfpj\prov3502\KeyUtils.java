package com.psbc.pfpj.prov3502;

import java.security.GeneralSecurityException;
import java.util.HashMap;

import com.pfpj.foundation.database.DatabaseExt;

public class KeyUtils {
	
	private static String KEY = "35020005031403DE35020005031403DE";
	
	/**
	 * 获取SM2公钥
	 * return SM2公钥
	 * */
	@SuppressWarnings("unchecked")
	public static String getPubKey() {
		byte[] pubKey = null;
		byte[] enPubKey = null;
		Object keysArray[] = DatabaseExt.queryByNamedSql("default", "com.psbc.pfpj.prov3502.security.getEnPubKey", null);
		if (keysArray.length == 1) {
			HashMap<String,Object> keysMap = (HashMap<String, Object>) keysArray[0];
			enPubKey = KeyUtils.hexStringsToByte((String) keysMap.get("pub_key"));
			try {
				pubKey = DesUtils.decryptBy3DesCbc(enPubKey, hexStringsToByte(KEY));
			} catch (GeneralSecurityException e) {
				e.printStackTrace();
			}
		}
		return KeyUtils.byteToHexStrings(pubKey);
	}
	
	/** 
     * 字节数组转十六进制字符
     * @param bytes 字节数组 
     * @return 转换结果
     */  
    public static String byteToHexStrings(byte[] bytes){  
    	StringBuilder stringBuilder = new StringBuilder();
    	if(bytes != null && bytes.length != 0) {
    		for (int i = 0; i < bytes.length; i++) {
    			String h = Integer.toHexString(bytes[i] & 0xFF);
    			if (h.length() < 2) {
    				stringBuilder.append("0");
    			}
    			stringBuilder.append(h);
			}
    	}
		return stringBuilder.toString().toUpperCase();
    }   

    /** 
     * 十六进制字符转字节数组
     * @param string 十六进制字符 
     * @return 转换结果
     */  
    public static byte[] hexStringsToByte(String string){  
    	byte[] bytes = null;
    	if (string.length() > 0 && string.length() % 2 == 0) {
    		bytes = new byte[string.length() / 2];
    		for (int i = 0; i < string.length(); i += 2) {
				String hv = string.substring(i, i+2).toUpperCase();
				bytes[i/2] = (byte) Integer.parseInt(hv, 16);
			}
    	}
		return bytes;
    }
    
    public static void main(String[] args) {
    	byte[] enPubKey = null;
		String pubKey = "043FD8B7BA3164601F65B4ACFF4F0CB966BCDBEBDB66D32A46F3A364039A78E9F0A9592F9CC7E38A7C86BE6D59586B7E5A3E07564982AD34CC4FF74E70943084F4";
		try {
			enPubKey = DesUtils.encryptBy3DesCbc(hexStringsToByte(pubKey), hexStringsToByte(KEY));
		} catch (GeneralSecurityException e) {
			e.printStackTrace();
		}
		System.out.println(byteToHexStrings(enPubKey));
		
		String enPubKeyString = "68543251CE666F155B85D1976A51AEE53F68CB126FCD7722F7D3013A768CFD841A5425CE181C072B76B75E9C0DBBB09D0CF05B9CE042CAB49428133E9299D19BCFE42E9E3FE6DE6F";
		enPubKey = KeyUtils.hexStringsToByte(enPubKeyString);
		try {
			pubKey = KeyUtils.byteToHexStrings(DesUtils.decryptBy3DesCbc(enPubKey, hexStringsToByte(KEY)));
		} catch (GeneralSecurityException e) {
			e.printStackTrace();
		}
		System.out.println(pubKey);
    }
    
}
