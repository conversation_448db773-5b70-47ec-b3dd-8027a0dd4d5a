<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="Administrator" category="transport-http" create-date="2021-04-28 17:11:13" version="*******">
  <transport-http id="bran_prov3502_xmweb">
    <name>bran_prov3502_xmweb</name>
    <encoding>UTF-8</encoding>
    <exception-translator></exception-translator>
    <request-param-name></request-param-name>
    <jetty-continuation-timeout>60</jetty-continuation-timeout>
    <is-record-message>false</is-record-message>
    <is-secure>false</is-secure>
    <file-transmission>
      <transmission-mode>ftp</transmission-mode>
      <port>22</port>
    </file-transmission>
    <description>省内web管理端接入，测试环境为12358</description>
    <host>0.0.0.0</host>
    <port>12358</port>
    <context>/</context>
    <protocol-type>http</protocol-type>
    <data-exchange-class>com.psbc.pfpj.prov3502.xmweb.XmwebHttpDataExchange</data-exchange-class>
    <transport-request-creator>com.psbc.pfpj.prov3502.xmweb.XmwebOutsysHttpTransportRequestCreator</transport-request-creator>
    <transport-request-handler>
      <min-size>50</min-size>
      <max-size>50</max-size>
      <increase-size>1</increase-size>
      <keep-alive-time>60</keep-alive-time>
      <checkout-timeout>5</checkout-timeout>
      <factory-class></factory-class>
    </transport-request-handler>
    <work-threads>
      <min-size>50</min-size>
      <max-size>50</max-size>
      <keep-alive-time>60</keep-alive-time>
      <queue-size>0</queue-size>
      <rejected-policy></rejected-policy>
    </work-threads>
    <ext-property/>
    <ext-class></ext-class>
    <max-length>20480</max-length>
    <is-verify-client>false</is-verify-client>
    <serialno-rule>UUID</serialno-rule>
    <target-system>************</target-system>
  </transport-http>
</configuration>