<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="Administrator" category="transport-tcp" create-date="2021-12-02 11:01:41" version="*******">
  <transport-tcp id="XMSPF_DS">
    <name>XMSPF_DS</name>
    <mode>HALF_DUPLEX</mode>
    <description>厦门商品房定时交易-外联定时脚本接入</description>
    <host>0.0.0.0</host>
    <port>14888</port>
    <max-socket-size>1000</max-socket-size>
    <is-short-socket>true</is-short-socket>
    <transport-request-creator>com.psbc.pfpj.prov3502.Xmspf_dsTcpTransportRequestCreator</transport-request-creator>
    <data-exchange-class>com.psbc.pfpj.prov3502.Xmspf_dsCustomTcpDataExchange</data-exchange-class>
    <transport-request-handler>
      <min-size>50</min-size>
      <max-size>50</max-size>
      <increase-size>1</increase-size>
      <keep-alive-time>60</keep-alive-time>
      <checkout-timeout>5</checkout-timeout>
      <factory-class></factory-class>
    </transport-request-handler>
    <work-threads>
      <min-size>50</min-size>
      <max-size>50</max-size>
      <keep-alive-time>60</keep-alive-time>
      <queue-size>1000</queue-size>
      <rejected-policy></rejected-policy>
    </work-threads>
    <work-threads-ref></work-threads-ref>
    <ext-property/>
    <ext-class></ext-class>
    <encoding>UTF-8</encoding>
    <is-record-message>false</is-record-message>
    <system-field-ref>xmspf_ds_head</system-field-ref>
    <message-ending mode="0">
      <offset>0</offset>
      <length>4</length>
      <initial-bytes-to-strip>4</initial-bytes-to-strip>
      <corrected-value>-4</corrected-value>
      <length-acquire-mode>2</length-acquire-mode>
    </message-ending>
    <max-length>2048</max-length>
    <is-multi>false</is-multi>
    <transport-num>5</transport-num>
    <endpoint-num>5</endpoint-num>
    <endpoint-ip>127.0.0.1</endpoint-ip>
    <endpoint-port>9003</endpoint-port>
    <is-secure>null</is-secure>
    <heart-beat-msg>com.primeton.btp.spi.transport.tcp.HeartBeatTest</heart-beat-msg>
    <serialno-rule>UUID</serialno-rule>
    <target-system>************</target-system>
  </transport-tcp>
</configuration>