<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_com.XMWEB_COM_OpeUpeREQ" xmlns:common_head_req="prov3502_common.common_head_req" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_com.XMWEB_COM_OpeUpeREQ">
    <xs:import namespace="prov3502_common.common_head_req" schemaLocation="../prov3502_common/common_head_req_msg_btx.xsd"/>
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="ope_nm" nillable="true" type="xs:string"/>
            <xs:element name="update_tm" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="XMWEB_COM_OpeUpeREQ">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="common_head_req:common_head_req"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
