<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="com.psbc.pfpj.prov3502.xmweb.LogTableOperation">
    <parameterMap class="commonj.sdo.DataObject" id="parameterMap">
        <parameter javaType="date" jdbcType="DATE" property="dateType"/>
    </parameterMap>
    <resultMap class="commonj.sdo.DataObject" id="resultMap">
        <result column="TYPEID" javaType="string" property="typeId"/>
    </resultMap>
    <!-- 新增中心流水表记录 -->
    <insert id="insert_log_record" parameterClass="java.util.HashMap">
		insert into tb_int_txn_log(
			tran_sq,
			tran_inst_id,
			tran_cd,
			tran_dt,
			<isNotEmpty property="tran_at">tran_at,</isNotEmpty>
        <isNotEmpty property="local_dt">local_dt,</isNotEmpty>
        <isNotEmpty property="node_type">node_type,</isNotEmpty>
        <isNotEmpty property="sole_front_sq">sole_front_sq,</isNotEmpty>
        <isNotEmpty property="local_tm">local_tm,</isNotEmpty>
        <isNotEmpty property="ini_tran_inst_id">ini_tran_inst_id,</isNotEmpty>
        <isNotEmpty property="chnl_cd">chnl_cd,</isNotEmpty>
        <isNotEmpty property="inst_term_id">inst_term_id,</isNotEmpty>
        <isNotEmpty property="out_sys_code">out_sys_code,</isNotEmpty>
        <isNotEmpty property="unite_clr_dt">unite_clr_dt,</isNotEmpty>
        <isNotEmpty property="acc_serial_sq">acc_serial_sq,</isNotEmpty>
        <isNotEmpty property="sub_ope_cd">sub_ope_cd,</isNotEmpty>
        <isNotEmpty property="pay_type_cd">pay_type_cd,</isNotEmpty>
        <isNotEmpty property="pay_id">pay_id,</isNotEmpty>
        <isNotEmpty property="payment_way_cd">payment_way_cd,</isNotEmpty>
        <isNotEmpty property="exec_dt">exec_dt,</isNotEmpty>
        <isNotEmpty property="check_id">check_id,</isNotEmpty>
        <isNotEmpty property="box_id">box_id,</isNotEmpty>
        <isNotEmpty property="op_inst_id">op_inst_id,</isNotEmpty>
        <isNotEmpty property="acc_card_id">acc_card_id,</isNotEmpty>
        <isNotEmpty property="merch_clr_dt">merch_clr_dt,</isNotEmpty>
        <isNotEmpty property="rsp_cd">rsp_cd,</isNotEmpty>
        <isNotEmpty property="pay_cd">pay_cd,</isNotEmpty>
        <isNotEmpty property="dz_cd">dz_cd,</isNotEmpty>
        <isNotEmpty property="clientdz_cd">clientdz_cd,</isNotEmpty>
        <isNotEmpty property="invoice_num">invoice_num,</isNotEmpty>
        <isNotEmpty property="invoice_head">invoice_head,</isNotEmpty>
        <isNotEmpty property="invoice_bodey">invoice_bodey,</isNotEmpty>
        <isNotEmpty property="tran_stat_cd">tran_stat_cd,</isNotEmpty>
        <isNotEmpty property="host_area_fee_at">host_area_fee_at,</isNotEmpty>
        <isNotEmpty property="bus_srv_fee_chrg_fg">bus_srv_fee_chrg_fg,</isNotEmpty>
        <isNotEmpty property="bus_srv_fee1_at">bus_srv_fee1_at,</isNotEmpty>
        <isNotEmpty property="bus_srv_fee2_at">bus_srv_fee2_at,</isNotEmpty>
        <isNotEmpty property="oth_fee_at">oth_fee_at,</isNotEmpty>
        <isNotEmpty property="disc_fee1_at">disc_fee1_at,</isNotEmpty>
        <isNotEmpty property="disc_fee2_at">disc_fee2_at,</isNotEmpty>
        <isNotEmpty property="out_op_bank_fg">out_op_bank_fg,</isNotEmpty>
        <isNotEmpty property="out_op_inst_id">out_op_inst_id,</isNotEmpty>
        <isNotEmpty property="out_acc_card_id">out_acc_card_id,</isNotEmpty>
        <isNotEmpty property="out_acc_card_nm">out_acc_card_nm,</isNotEmpty>
        <isNotEmpty property="in_op_bank_fg">in_op_bank_fg,</isNotEmpty>
        <isNotEmpty property="in_op_inst_id">in_op_inst_id,</isNotEmpty>
        <isNotEmpty property="in_acc_card_id">in_acc_card_id,</isNotEmpty>
        <isNotEmpty property="in_acc_card_nm">in_acc_card_nm,</isNotEmpty>
        <isNotEmpty property="bak_fg">bak_fg,</isNotEmpty>
        <isNotEmpty property="oth_at">oth_at,</isNotEmpty>
        <isNotEmpty property="cstm_id">cstm_id,</isNotEmpty>
        <isNotEmpty property="org_tran_dt">org_tran_dt,</isNotEmpty>
        <isNotEmpty property="org_tran_sq">org_tran_sq,</isNotEmpty>
        <isNotEmpty property="checker_id">checker_id,</isNotEmpty>
        <isNotEmpty property="auth_type">auth_type,</isNotEmpty>
        <isNotEmpty property="oth_msg_tx">oth_msg_tx,</isNotEmpty>
        <isNotEmpty property="sys_dt">sys_dt,</isNotEmpty>
        <isNotEmpty property="sys_tm">sys_tm,</isNotEmpty>
        <isNotEmpty property="sys_refer_no">sys_refer_no,</isNotEmpty>
        <isNotEmpty property="log_id">log_id,</isNotEmpty>
        <isNotEmpty property="oth_msg1_tx">oth_msg1_tx,</isNotEmpty>
        <isNotEmpty property="oth_msg2_tx">oth_msg2_tx,</isNotEmpty>
        <isNotEmpty property="oth_msg3_tx">oth_msg3_tx,</isNotEmpty>
        <isNotEmpty property="oth_msg4_tx">oth_msg4_tx,</isNotEmpty>
        <isNotEmpty property="oth_msg5_tx">oth_msg5_tx,</isNotEmpty>
        <isNotEmpty property="oth_msg6_tx">oth_msg6_tx,</isNotEmpty>
        <isNotEmpty property="oth_msg7_tx">oth_msg7_tx,</isNotEmpty>
        <isNotEmpty property="oth_msg8_tx">oth_msg8_tx,</isNotEmpty>
        <isNotEmpty property="oth1_fg">oth1_fg,</isNotEmpty>
        <isNotEmpty property="oth2_fg">oth2_fg,</isNotEmpty>
        <isNotEmpty property="oth3_fg">oth3_fg,</isNotEmpty>
        <isNotEmpty property="commi_inst_id">commi_inst_id,</isNotEmpty>
        <isNotEmpty property="chnl_out_sys_code">chnl_out_sys_code,</isNotEmpty>
        <isNotEmpty property="chnl_sys_refer_no">chnl_sys_refer_no,</isNotEmpty>
        <isNotEmpty property="cli_serial_no">cli_serial_no,</isNotEmpty>
        <isNotEmpty property="tx_namecode">tx_namecode,</isNotEmpty>
        <isNotEmpty property="cust_area_chara">cust_area_chara,</isNotEmpty>
        <isNotEmpty property="last_tm">last_tm,</isNotEmpty>
        <isNotEmpty property="oth_co_msg1">oth_co_msg1,</isNotEmpty>
        <isNotEmpty property="oth_co_msg2">oth_co_msg2,</isNotEmpty>
        <isNotEmpty property="oth_co_msg3">oth_co_msg3,</isNotEmpty>
        <isNotEmpty property="oth_pr_msg1">oth_pr_msg1,</isNotEmpty>
        <isNotEmpty property="oth_pr_msg2">oth_pr_msg2,</isNotEmpty>
        <isNotEmpty property="oth_pr_msg3">oth_pr_msg3,</isNotEmpty>	
			merch_id,
			ope_cd,
			tlr_id
		) values (
			#tran_sq#,
			#tran_inst_id#,
			#tran_cd#,
			#tran_dt#,
			<isNotEmpty property="tran_at">cast(#tran_at# as numeric),</isNotEmpty>
        <isNotEmpty property="local_dt">#local_dt#,</isNotEmpty>
        <isNotEmpty property="node_type">#node_type#,</isNotEmpty>
        <isNotEmpty property="sole_front_sq">#sole_front_sq#,</isNotEmpty>
        <isNotEmpty property="local_tm">#local_tm#,</isNotEmpty>
        <isNotEmpty property="ini_tran_inst_id">#ini_tran_inst_id#,</isNotEmpty>
        <isNotEmpty property="chnl_cd">cast(#chnl_cd# as numeric),</isNotEmpty>
        <isNotEmpty property="inst_term_id">#inst_term_id#,</isNotEmpty>
        <isNotEmpty property="out_sys_code">#out_sys_code#,</isNotEmpty>
        <isNotEmpty property="unite_clr_dt">#unite_clr_dt#,</isNotEmpty>
        <isNotEmpty property="acc_serial_sq">#acc_serial_sq#,</isNotEmpty>
        <isNotEmpty property="sub_ope_cd">#sub_ope_cd#,</isNotEmpty>
        <isNotEmpty property="pay_type_cd">#pay_type_cd#,</isNotEmpty>
        <isNotEmpty property="pay_id">#pay_id#,</isNotEmpty>
        <isNotEmpty property="payment_way_cd">#payment_way_cd#,</isNotEmpty>
        <isNotEmpty property="exec_dt">#exec_dt#,</isNotEmpty>
        <isNotEmpty property="check_id">#check_id#,</isNotEmpty>
        <isNotEmpty property="box_id">#box_id#,</isNotEmpty>
        <isNotEmpty property="op_inst_id">#op_inst_id#,</isNotEmpty>
        <isNotEmpty property="acc_card_id">#acc_card_id#,</isNotEmpty>
        <isNotEmpty property="merch_clr_dt">#merch_clr_dt#,</isNotEmpty>
        <isNotEmpty property="rsp_cd">#rsp_cd#,</isNotEmpty>
        <isNotEmpty property="pay_cd">#pay_cd#,</isNotEmpty>
        <isNotEmpty property="dz_cd">#dz_cd#,</isNotEmpty>
        <isNotEmpty property="clientdz_cd">#clientdz_cd#,</isNotEmpty>
        <isNotEmpty property="invoice_num">#invoice_num#,</isNotEmpty>
        <isNotEmpty property="invoice_head">#invoice_head#,</isNotEmpty>
        <isNotEmpty property="invoice_bodey">#invoice_bodey#,</isNotEmpty>
        <isNotEmpty property="tran_stat_cd">#tran_stat_cd#,</isNotEmpty>
        <isNotEmpty property="host_area_fee_at">cast(#host_area_fee_at# as numeric),</isNotEmpty>
        <isNotEmpty property="bus_srv_fee_chrg_fg">#bus_srv_fee_chrg_fg#,</isNotEmpty>
        <isNotEmpty property="bus_srv_fee1_at">cast(#bus_srv_fee1_at# as numeric),</isNotEmpty>
        <isNotEmpty property="bus_srv_fee2_at">cast(#bus_srv_fee2_at# as numeric),</isNotEmpty>
        <isNotEmpty property="oth_fee_at">cast(#oth_fee_at# as numeric),</isNotEmpty>
        <isNotEmpty property="disc_fee1_at">cast(#disc_fee1_at# as numeric),</isNotEmpty>
        <isNotEmpty property="disc_fee2_at">cast(#disc_fee2_at# as numeric),</isNotEmpty>
        <isNotEmpty property="out_op_bank_fg">#out_op_bank_fg#,</isNotEmpty>
        <isNotEmpty property="out_op_inst_id">#out_op_inst_id#,</isNotEmpty>
        <isNotEmpty property="out_acc_card_id">#out_acc_card_id#,</isNotEmpty>
        <isNotEmpty property="out_acc_card_nm">#out_acc_card_nm#,</isNotEmpty>
        <isNotEmpty property="in_op_bank_fg">#in_op_bank_fg#,</isNotEmpty>
        <isNotEmpty property="in_op_inst_id">#in_op_inst_id#,</isNotEmpty>
        <isNotEmpty property="in_acc_card_id">#in_acc_card_id#,</isNotEmpty>
        <isNotEmpty property="in_acc_card_nm">#in_acc_card_nm#,</isNotEmpty>
        <isNotEmpty property="bak_fg">#bak_fg#,</isNotEmpty>
        <isNotEmpty property="oth_at">cast(#oth_at# as numeric),</isNotEmpty>
        <isNotEmpty property="cstm_id">#cstm_id#,</isNotEmpty>
        <isNotEmpty property="org_tran_dt">#org_tran_dt#,</isNotEmpty>
        <isNotEmpty property="org_tran_sq">#org_tran_sq#,</isNotEmpty>
        <isNotEmpty property="checker_id">#checker_id#,</isNotEmpty>
        <isNotEmpty property="auth_type">#auth_type#,</isNotEmpty>
        <isNotEmpty property="oth_msg_tx">#oth_msg_tx#,</isNotEmpty>
        <isNotEmpty property="sys_dt">#sys_dt#,</isNotEmpty>
        <isNotEmpty property="sys_tm">#sys_tm#,</isNotEmpty>
        <isNotEmpty property="sys_refer_no">#sys_refer_no#,</isNotEmpty>
        <isNotEmpty property="log_id">#log_id#,</isNotEmpty>
        <isNotEmpty property="oth_msg1_tx">#oth_msg1_tx#,</isNotEmpty>
        <isNotEmpty property="oth_msg2_tx">#oth_msg2_tx#,</isNotEmpty>
        <isNotEmpty property="oth_msg3_tx">#oth_msg3_tx#,</isNotEmpty>
        <isNotEmpty property="oth_msg4_tx">#oth_msg4_tx#,</isNotEmpty>
        <isNotEmpty property="oth_msg5_tx">#oth_msg5_tx#,</isNotEmpty>
        <isNotEmpty property="oth_msg6_tx">#oth_msg6_tx#,</isNotEmpty>
        <isNotEmpty property="oth_msg7_tx">#oth_msg7_tx#,</isNotEmpty>
        <isNotEmpty property="oth_msg8_tx">#oth_msg8_tx#,</isNotEmpty>
        <isNotEmpty property="oth1_fg">#oth1_fg#,</isNotEmpty>
        <isNotEmpty property="oth2_fg">#oth2_fg#,</isNotEmpty>
        <isNotEmpty property="oth3_fg">#oth3_fg#,</isNotEmpty>
        <isNotEmpty property="commi_inst_id">#commi_inst_id#,</isNotEmpty>
        <isNotEmpty property="chnl_out_sys_code">#chnl_out_sys_code#,</isNotEmpty>
        <isNotEmpty property="chnl_sys_refer_no">#chnl_sys_refer_no#,</isNotEmpty>
        <isNotEmpty property="cli_serial_no">#cli_serial_no#,</isNotEmpty>
        <isNotEmpty property="tx_namecode">#tx_namecode#,</isNotEmpty>
        <isNotEmpty property="cust_area_chara">cast(#cust_area_chara# as numeric),</isNotEmpty>
        <isNotEmpty property="last_tm">#last_tm#,</isNotEmpty>
        <isNotEmpty property="oth_co_msg1">#oth_co_msg1#,</isNotEmpty>
        <isNotEmpty property="oth_co_msg2">#oth_co_msg2#,</isNotEmpty>
        <isNotEmpty property="oth_co_msg3">#oth_co_msg3#,</isNotEmpty>
        <isNotEmpty property="oth_pr_msg1">#oth_pr_msg1#,</isNotEmpty>
        <isNotEmpty property="oth_pr_msg2">#oth_pr_msg2#,</isNotEmpty>
        <isNotEmpty property="oth_pr_msg3">#oth_pr_msg3#,</isNotEmpty>
			#merch_id#,
			#ope_cd#,
			#tlr_id#
	   	)
	</insert>
    <!-- 修改中心流水表记录 -->
    <update id="update_log_record" parameterClass="java.util.HashMap">
		update 
			tb_int_txn_log 
		set
			merch_id = #merch_id#,
			<isNotEmpty property="tran_sq">tran_sq = #tran_sq#,</isNotEmpty>
        <isNotEmpty property="tran_inst_id">tran_inst_id = #tran_inst_id#,</isNotEmpty>
        <isNotEmpty property="tran_cd">tran_cd = #tran_cd#,</isNotEmpty>
        <isNotEmpty property="tran_dt">tran_dt = #tran_dt#,</isNotEmpty>
        <isNotEmpty property="tran_at">tran_at = cast(#,tran_at#, as numeric)</isNotEmpty>
        <isNotEmpty property="local_dt">local_dt = #local_dt#,</isNotEmpty>
        <isNotEmpty property="node_type">node_type = #node_type#,</isNotEmpty>
        <isNotEmpty property="sole_front_sq">sole_front_sq = #sole_front_sq#,</isNotEmpty>
        <isNotEmpty property="local_tm">local_tm = #local_tm#,</isNotEmpty>
        <isNotEmpty property="ini_tran_inst_id">ini_tran_inst_id = #ini_tran_inst_id#,</isNotEmpty>
        <isNotEmpty property="chnl_cd">chnl_cd = cast(#,chnl_cd#, as numeric)</isNotEmpty>
        <isNotEmpty property="inst_term_id">inst_term_id = #inst_term_id#,</isNotEmpty>
        <isNotEmpty property="out_sys_code">out_sys_code = #out_sys_code#,</isNotEmpty>
        <isNotEmpty property="unite_clr_dt">unite_clr_dt = #unite_clr_dt#,</isNotEmpty>
        <isNotEmpty property="acc_serial_sq">acc_serial_sq = #acc_serial_sq#,</isNotEmpty>
        <isNotEmpty property="sub_ope_cd">sub_ope_cd = #sub_ope_cd#,</isNotEmpty>
        <isNotEmpty property="pay_type_cd">pay_type_cd = #pay_type_cd#,</isNotEmpty>
        <isNotEmpty property="pay_id">pay_id = #pay_id#,</isNotEmpty>
        <isNotEmpty property="payment_way_cd">payment_way_cd = #payment_way_cd#,</isNotEmpty>
        <isNotEmpty property="exec_dt">exec_dt = #exec_dt#,</isNotEmpty>
        <isNotEmpty property="check_id">check_id = #check_id#,</isNotEmpty>
        <isNotEmpty property="box_id">box_id = #box_id#,</isNotEmpty>
        <isNotEmpty property="op_inst_id">op_inst_id = #op_inst_id#,</isNotEmpty>
        <isNotEmpty property="acc_card_id">acc_card_id = #acc_card_id#,</isNotEmpty>
        <isNotEmpty property="merch_clr_dt">merch_clr_dt = #merch_clr_dt#,</isNotEmpty>
        <isNotEmpty property="rsp_cd">rsp_cd = #rsp_cd#,</isNotEmpty>
        <isNotEmpty property="pay_cd">pay_cd = #pay_cd#,</isNotEmpty>
        <isNotEmpty property="dz_cd">dz_cd = #dz_cd#,</isNotEmpty>
        <isNotEmpty property="clientdz_cd">clientdz_cd = #clientdz_cd#,</isNotEmpty>
        <isNotEmpty property="invoice_num">invoice_num = #invoice_num#,</isNotEmpty>
        <isNotEmpty property="invoice_head">invoice_head = #invoice_head#,</isNotEmpty>
        <isNotEmpty property="invoice_bodey">invoice_bodey = #invoice_bodey#,</isNotEmpty>
        <isNotEmpty property="tran_stat_cd">tran_stat_cd = #tran_stat_cd#,</isNotEmpty>
        <isNotEmpty property="host_area_fee_at">host_area_fee_at = cast(#,host_area_fee_at#, as numeric)</isNotEmpty>
        <isNotEmpty property="bus_srv_fee_chrg_fg">bus_srv_fee_chrg_fg = #bus_srv_fee_chrg_fg#,</isNotEmpty>
        <isNotEmpty property="bus_srv_fee1_at">bus_srv_fee1_at = cast(#,bus_srv_fee1_at#, as numeric)</isNotEmpty>
        <isNotEmpty property="bus_srv_fee2_at">bus_srv_fee2_at = cast(#,bus_srv_fee2_at#, as numeric)</isNotEmpty>
        <isNotEmpty property="oth_fee_at">oth_fee_at = cast(#,oth_fee_at#, as numeric)</isNotEmpty>
        <isNotEmpty property="disc_fee1_at">disc_fee1_at = cast(#,disc_fee1_at#, as numeric)</isNotEmpty>
        <isNotEmpty property="disc_fee2_at">disc_fee2_at = cast(#,disc_fee2_at#, as numeric)</isNotEmpty>
        <isNotEmpty property="out_op_bank_fg">out_op_bank_fg = #out_op_bank_fg#,</isNotEmpty>
        <isNotEmpty property="out_op_inst_id">out_op_inst_id = #out_op_inst_id#,</isNotEmpty>
        <isNotEmpty property="out_acc_card_id">out_acc_card_id = #out_acc_card_id#,</isNotEmpty>
        <isNotEmpty property="out_acc_card_nm">out_acc_card_nm = #out_acc_card_nm#,</isNotEmpty>
        <isNotEmpty property="in_op_bank_fg">in_op_bank_fg = #in_op_bank_fg#,</isNotEmpty>
        <isNotEmpty property="in_op_inst_id">in_op_inst_id = #in_op_inst_id#,</isNotEmpty>
        <isNotEmpty property="in_acc_card_id">in_acc_card_id = #in_acc_card_id#,</isNotEmpty>
        <isNotEmpty property="in_acc_card_nm">in_acc_card_nm = #in_acc_card_nm#,</isNotEmpty>
        <isNotEmpty property="bak_fg">bak_fg = #bak_fg#,</isNotEmpty>
        <isNotEmpty property="oth_at">oth_at = cast(#,oth_at#, as numeric)</isNotEmpty>
        <isNotEmpty property="cstm_id">cstm_id = #cstm_id#,</isNotEmpty>
        <isNotEmpty property="org_tran_dt">org_tran_dt = #org_tran_dt#,</isNotEmpty>
        <isNotEmpty property="org_tran_sq">org_tran_sq = #org_tran_sq#,</isNotEmpty>
        <isNotEmpty property="checker_id">checker_id = #checker_id#,</isNotEmpty>
        <isNotEmpty property="auth_type">auth_type = #auth_type#,</isNotEmpty>
        <isNotEmpty property="oth_msg_tx">oth_msg_tx = #oth_msg_tx#,</isNotEmpty>
        <isNotEmpty property="sys_dt">sys_dt = #sys_dt#,</isNotEmpty>
        <isNotEmpty property="sys_tm">sys_tm = #sys_tm#,</isNotEmpty>
        <isNotEmpty property="sys_refer_no">sys_refer_no = #sys_refer_no#,</isNotEmpty>
        <isNotEmpty property="log_id">log_id = #log_id#,</isNotEmpty>
        <isNotEmpty property="oth_msg1_tx">oth_msg1_tx = #oth_msg1_tx#,</isNotEmpty>
        <isNotEmpty property="oth_msg2_tx">oth_msg2_tx = #oth_msg2_tx#,</isNotEmpty>
        <isNotEmpty property="oth_msg3_tx">oth_msg3_tx = #oth_msg3_tx#,</isNotEmpty>
        <isNotEmpty property="oth_msg4_tx">oth_msg4_tx = #oth_msg4_tx#,</isNotEmpty>
        <isNotEmpty property="oth_msg5_tx">oth_msg5_tx = #oth_msg5_tx#,</isNotEmpty>
        <isNotEmpty property="oth_msg6_tx">oth_msg6_tx = #oth_msg6_tx#,</isNotEmpty>
        <isNotEmpty property="oth_msg7_tx">oth_msg7_tx = #oth_msg7_tx#,</isNotEmpty>
        <isNotEmpty property="oth_msg8_tx">oth_msg8_tx = #oth_msg8_tx#,</isNotEmpty>
        <isNotEmpty property="oth1_fg">oth1_fg = #oth1_fg#,</isNotEmpty>
        <isNotEmpty property="oth2_fg">oth2_fg = #oth2_fg#,</isNotEmpty>
        <isNotEmpty property="oth3_fg">oth3_fg = #oth3_fg#,</isNotEmpty>
        <isNotEmpty property="commi_inst_id">commi_inst_id = #commi_inst_id#,</isNotEmpty>
        <isNotEmpty property="chnl_out_sys_code">chnl_out_sys_code = #chnl_out_sys_code#,</isNotEmpty>
        <isNotEmpty property="chnl_sys_refer_no">chnl_sys_refer_no = #chnl_sys_refer_no#,</isNotEmpty>
        <isNotEmpty property="cli_serial_no">cli_serial_no = #cli_serial_no#,</isNotEmpty>
        <isNotEmpty property="tx_namecode">tx_namecode = #tx_namecode#,</isNotEmpty>
        <isNotEmpty property="cust_area_chara">cust_area_chara = cast(#,cust_area_chara#, as numeric)</isNotEmpty>
        <isNotEmpty property="last_tm">last_tm = #last_tm#,</isNotEmpty>
        <isNotEmpty property="oth_co_msg1">oth_co_msg1 = #oth_co_msg1#,</isNotEmpty>
        <isNotEmpty property="oth_co_msg2">oth_co_msg2 = #oth_co_msg2#,</isNotEmpty>
        <isNotEmpty property="oth_co_msg3">oth_co_msg3 = #oth_co_msg3#,</isNotEmpty>
        <isNotEmpty property="oth_pr_msg1">oth_pr_msg1 = #oth_pr_msg1#,</isNotEmpty>
        <isNotEmpty property="oth_pr_msg2">oth_pr_msg2 = #oth_pr_msg2#,</isNotEmpty>
        <isNotEmpty property="oth_pr_msg3">oth_pr_msg3 = #oth_pr_msg3#,</isNotEmpty>
        <isNotEmpty property="tlr_id">tlr_id = #tlr_id#,</isNotEmpty>
			ope_cd = #ope_cd#
		where
			merch_id = #merch_id#
			and ope_cd = #ope_cd#
			<isNotEmpty property="old_tran_sq">and tran_sq = #old_tran_sq#</isNotEmpty>
        <isNotEmpty property="old_tran_inst_id">and tran_inst_id = #old_tran_inst_id#</isNotEmpty>
        <isNotEmpty property="old_tran_cd">and tran_cd = #old_tran_cd#</isNotEmpty>
        <isNotEmpty property="old_tran_dt">and tran_dt = #old_tran_dt#</isNotEmpty>
        <isNotEmpty property="old_tran_at">and tran_at = cast(#old_tran_at# as numeric)</isNotEmpty>
        <isNotEmpty property="old_local_dt">and local_dt = #old_local_dt#</isNotEmpty>
        <isNotEmpty property="old_node_type">and node_type = #old_node_type#</isNotEmpty>
        <isNotEmpty property="old_sole_front_sq">and sole_front_sq = #old_sole_front_sq#</isNotEmpty>
        <isNotEmpty property="old_local_tm">and local_tm = #old_local_tm#</isNotEmpty>
        <isNotEmpty property="old_ini_tran_inst_id">and ini_tran_inst_id = #old_ini_tran_inst_id#</isNotEmpty>
        <isNotEmpty property="old_chnl_cd">and chnl_cd = cast(#old_chnl_cd# as numeric)</isNotEmpty>
        <isNotEmpty property="old_inst_term_id">and inst_term_id = #old_inst_term_id#</isNotEmpty>
        <isNotEmpty property="old_out_sys_code">and out_sys_code = #old_out_sys_code#</isNotEmpty>
        <isNotEmpty property="old_unite_clr_dt">and unite_clr_dt = #old_unite_clr_dt#</isNotEmpty>
        <isNotEmpty property="old_acc_serial_sq">and acc_serial_sq = #old_acc_serial_sq#</isNotEmpty>
        <isNotEmpty property="old_sub_ope_cd">and sub_ope_cd = #old_sub_ope_cd#</isNotEmpty>
        <isNotEmpty property="old_pay_type_cd">and pay_type_cd = #old_pay_type_cd#</isNotEmpty>
        <isNotEmpty property="old_pay_id">and pay_id = #old_pay_id#</isNotEmpty>
        <isNotEmpty property="old_payment_way_cd">and payment_way_cd = #old_payment_way_cd#</isNotEmpty>
        <isNotEmpty property="old_exec_dt">and exec_dt = #old_exec_dt#</isNotEmpty>
        <isNotEmpty property="old_check_id">and check_id = #old_check_id#</isNotEmpty>
        <isNotEmpty property="old_box_id">and box_id = #old_box_id#</isNotEmpty>
        <isNotEmpty property="old_op_inst_id">and op_inst_id = #old_op_inst_id#</isNotEmpty>
        <isNotEmpty property="old_acc_card_id">and acc_card_id = #old_acc_card_id#</isNotEmpty>
        <isNotEmpty property="old_merch_clr_dt">and merch_clr_dt = #old_merch_clr_dt#</isNotEmpty>
        <isNotEmpty property="old_rsp_cd">and rsp_cd = #old_rsp_cd#</isNotEmpty>
        <isNotEmpty property="old_pay_cd">and pay_cd = #old_pay_cd#</isNotEmpty>
        <isNotEmpty property="old_dz_cd">and dz_cd = #old_dz_cd#</isNotEmpty>
        <isNotEmpty property="old_clientdz_cd">and clientdz_cd = #old_clientdz_cd#</isNotEmpty>
        <isNotEmpty property="old_invoice_num">and invoice_num = #old_invoice_num#</isNotEmpty>
        <isNotEmpty property="old_invoice_head">and invoice_head = #old_invoice_head#</isNotEmpty>
        <isNotEmpty property="old_invoice_bodey">and invoice_bodey = #old_invoice_bodey#</isNotEmpty>
        <isNotEmpty property="old_tran_stat_cd">and tran_stat_cd = #old_tran_stat_cd#</isNotEmpty>
        <isNotEmpty property="old_host_area_fee_at">and host_area_fee_at = cast(#old_host_area_fee_at# as numeric)</isNotEmpty>
        <isNotEmpty property="old_bus_srv_fee_chrg_fg">and bus_srv_fee_chrg_fg = #old_bus_srv_fee_chrg_fg#</isNotEmpty>
        <isNotEmpty property="old_bus_srv_fee1_at">and bus_srv_fee1_at = cast(#old_bus_srv_fee1_at# as numeric)</isNotEmpty>
        <isNotEmpty property="old_bus_srv_fee2_at">and bus_srv_fee2_at = cast(#old_bus_srv_fee2_at# as numeric)</isNotEmpty>
        <isNotEmpty property="old_oth_fee_at">and oth_fee_at = cast(#old_oth_fee_at# as numeric)</isNotEmpty>
        <isNotEmpty property="old_disc_fee1_at">and disc_fee1_at = cast(#old_disc_fee1_at# as numeric)</isNotEmpty>
        <isNotEmpty property="old_disc_fee2_at">and disc_fee2_at = cast(#old_disc_fee2_at# as numeric)</isNotEmpty>
        <isNotEmpty property="old_out_op_bank_fg">and out_op_bank_fg = #old_out_op_bank_fg#</isNotEmpty>
        <isNotEmpty property="old_out_op_inst_id">and out_op_inst_id = #old_out_op_inst_id#</isNotEmpty>
        <isNotEmpty property="old_out_acc_card_id">and out_acc_card_id = #old_out_acc_card_id#</isNotEmpty>
        <isNotEmpty property="old_out_acc_card_nm">and out_acc_card_nm = #old_out_acc_card_nm#</isNotEmpty>
        <isNotEmpty property="old_in_op_bank_fg">and in_op_bank_fg = #old_in_op_bank_fg#</isNotEmpty>
        <isNotEmpty property="old_in_op_inst_id">and in_op_inst_id = #old_in_op_inst_id#</isNotEmpty>
        <isNotEmpty property="old_in_acc_card_id">and in_acc_card_id = #old_in_acc_card_id#</isNotEmpty>
        <isNotEmpty property="old_in_acc_card_nm">and in_acc_card_nm = #old_in_acc_card_nm#</isNotEmpty>
        <isNotEmpty property="old_bak_fg">and bak_fg = #old_bak_fg#</isNotEmpty>
        <isNotEmpty property="old_oth_at">and oth_at = cast(#old_oth_at# as numeric)</isNotEmpty>
        <isNotEmpty property="old_cstm_id">and cstm_id = #old_cstm_id#</isNotEmpty>
        <isNotEmpty property="old_org_tran_dt">and org_tran_dt = #old_org_tran_dt#</isNotEmpty>
        <isNotEmpty property="old_org_tran_sq">and org_tran_sq = #old_org_tran_sq#</isNotEmpty>
        <isNotEmpty property="old_checker_id">and checker_id = #old_checker_id#</isNotEmpty>
        <isNotEmpty property="old_auth_type">and auth_type = #old_auth_type#</isNotEmpty>
        <isNotEmpty property="old_oth_msg_tx">and oth_msg_tx = #old_oth_msg_tx#</isNotEmpty>
        <isNotEmpty property="old_sys_dt">and sys_dt = #old_sys_dt#</isNotEmpty>
        <isNotEmpty property="old_sys_tm">and sys_tm = #old_sys_tm#</isNotEmpty>
        <isNotEmpty property="old_sys_refer_no">and sys_refer_no = #old_sys_refer_no#</isNotEmpty>
        <isNotEmpty property="old_log_id">and log_id = #old_log_id#</isNotEmpty>
        <isNotEmpty property="old_oth_msg1_tx">and oth_msg1_tx = #old_oth_msg1_tx#</isNotEmpty>
        <isNotEmpty property="old_oth_msg2_tx">and oth_msg2_tx = #old_oth_msg2_tx#</isNotEmpty>
        <isNotEmpty property="old_oth_msg3_tx">and oth_msg3_tx = #old_oth_msg3_tx#</isNotEmpty>
        <isNotEmpty property="old_oth_msg4_tx">and oth_msg4_tx = #old_oth_msg4_tx#</isNotEmpty>
        <isNotEmpty property="old_oth_msg5_tx">and oth_msg5_tx = #old_oth_msg5_tx#</isNotEmpty>
        <isNotEmpty property="old_oth_msg6_tx">and oth_msg6_tx = #old_oth_msg6_tx#</isNotEmpty>
        <isNotEmpty property="old_oth_msg7_tx">and oth_msg7_tx = #old_oth_msg7_tx#</isNotEmpty>
        <isNotEmpty property="old_oth_msg8_tx">and oth_msg8_tx = #old_oth_msg8_tx#</isNotEmpty>
        <isNotEmpty property="old_oth1_fg">and oth1_fg = #old_oth1_fg#</isNotEmpty>
        <isNotEmpty property="old_oth2_fg">and oth2_fg = #old_oth2_fg#</isNotEmpty>
        <isNotEmpty property="old_oth3_fg">and oth3_fg = #old_oth3_fg#</isNotEmpty>
        <isNotEmpty property="old_commi_inst_id">and commi_inst_id = #old_commi_inst_id#</isNotEmpty>
        <isNotEmpty property="old_chnl_out_sys_code">and chnl_out_sys_code = #old_chnl_out_sys_code#</isNotEmpty>
        <isNotEmpty property="old_chnl_sys_refer_no">and chnl_sys_refer_no = #old_chnl_sys_refer_no#</isNotEmpty>
        <isNotEmpty property="old_cli_serial_no">and cli_serial_no = #old_cli_serial_no#</isNotEmpty>
        <isNotEmpty property="old_tx_namecode">and tx_namecode = #old_tx_namecode#</isNotEmpty>
        <isNotEmpty property="old_cust_area_chara">and cust_area_chara = cast(#old_cust_area_chara# as numeric)</isNotEmpty>
        <isNotEmpty property="old_last_tm">and last_tm = #old_last_tm#</isNotEmpty>
        <isNotEmpty property="old_oth_co_msg1">and oth_co_msg1 = #old_oth_co_msg1#</isNotEmpty>
        <isNotEmpty property="old_oth_co_msg2">and oth_co_msg2 = #old_oth_co_msg2#</isNotEmpty>
        <isNotEmpty property="old_oth_co_msg3">and oth_co_msg3 = #old_oth_co_msg3#</isNotEmpty>
        <isNotEmpty property="old_oth_pr_msg1">and oth_pr_msg1 = #old_oth_pr_msg1#</isNotEmpty>
        <isNotEmpty property="old_oth_pr_msg2">and oth_pr_msg2 = #old_oth_pr_msg2#</isNotEmpty>
        <isNotEmpty property="old_oth_pr_msg3">and oth_pr_msg3 = #old_oth_pr_msg3#</isNotEmpty>
        <isNotEmpty property="old_tlr_id">and tlr_id = #old_tlr_id#</isNotEmpty>
    </update>
</sqlMap>