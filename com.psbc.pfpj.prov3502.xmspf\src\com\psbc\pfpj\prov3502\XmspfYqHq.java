/**
 * 
 */
package com.psbc.pfpj.prov3502;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.Socket;

import com.eos.system.annotation.Bizlet;
import com.primeton.btp.api.core.logger.ILogger;
import com.primeton.btp.api.core.logger.LoggerFactory;

/**
 * <AUTHOR>
 * @date 2021-09-06 07:08:46
 *
 */
@Bizlet("")
public class XmspfYqHq {
	private static ILogger logger = LoggerFactory.getLogger(XmspfYqHq.class);

	private static final String ENCODING = "utf-8";
	private static final int LENGTH = 6; //长度域的长度
	private static String ip = GetProperties.ip;
	private static int port = GetProperties.port;
	
	private static String rspmsg = null;//应答的原文、密文
	private static String tstamp = null;//应答的时间戳
	
	/**
	 * 
	 * @param content 消息体 原文/密文
	 */
	@Bizlet("调用主机服务")
	public static String SendSocket(String content) {
		logger.info("请求外挂=========");
		String reqmsg = null;
		String retcode = null; //应答的响应码
		String in_tran_cd_yd = null; //应答的交易码
		
		BufferedOutputStream bos = null;
		BufferedInputStream bis = null;
		
		Socket socket = null;
		try {
			//拼接请求报文
			reqmsg = String.format("%06d%s|@|",content.getBytes(ENCODING).length)+content;
			
			logger.info("请求内容:" + reqmsg);
			logger.info("委托方ip:" + ip + " 端口:" + port);
			//连接sock
			socket = new Socket(ip, port);
			socket.setSoTimeout(5000000);//超时
			socket.setTcpNoDelay(true);
			bos = new BufferedOutputStream(socket.getOutputStream());
			bos.write(reqmsg.getBytes(ENCODING));
			//logger.info("发送报文:" + reqmsg + "===============" + reqmsg.getBytes(ENCODING));
			bos.flush();
			
			// 获取委托方服务端输入流
			bis = new BufferedInputStream(socket.getInputStream());
			//根据6位长度域中长度，取报文
			byte[] bmsglen = new byte[LENGTH];
			bis.read(bmsglen);
			String smsglen = new String(bmsglen);
			int msglen = Integer.parseInt(smsglen);
			logger.info("长度域中的长度:"+msglen);
			
			byte[] rbmsg = new byte[msglen];
			bis.read(rbmsg);
			String respmsg = new String(rbmsg,ENCODING);
			logger.info("除长度域外的应答信息:"+respmsg);
			
			return respmsg;
			
			
		} catch (UnsupportedEncodingException e) {
			// TODO 自动生成的 catch 块
			e.printStackTrace();
		} catch (IOException e) {
			// TODO 自动生成的 catch 块
			e.printStackTrace();
		}
		return null;
	}
	
}
