<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="Administrator" category="outbound" create-date="2021-05-25 20:04:37" version="*******">
  <outbound id="HOSTS001">
    <name>HOSTS001</name>
    <description>外联到后台通用接出服务</description>
    <timeout>0</timeout>
    <timeout-action>IGNORE</timeout-action>
    <host-trans-code></host-trans-code>
    <host-cancel-service-id></host-cancel-service-id>
    <endpoint-id>host_prov3502_core</endpoint-id>
    <req-message-id>HOSTS001REQ</req-message-id>
    <resp-message-id>HOSTS001RES</resp-message-id>
    <exception-message-id>HOSTS001EXCE</exception-message-id>
    <ext-property>
      <entry description="超时处理类，必须实现接口com.primeton.btp.api.engine.hosttrans.ICustomTimeoutAction" key="CUSTOM_TIMEOUT_ACTION_CLASSNAME" value=""/>
    </ext-property>
    <ext-class></ext-class>
    <is-one-way>false</is-one-way>
    <fill-type>COVER</fill-type>
    <fill-pool>true</fill-pool>
    <host-server-name>CHN_SVC</host-server-name>
    <msg2file>false</msg2file>
  </outbound>
</configuration>