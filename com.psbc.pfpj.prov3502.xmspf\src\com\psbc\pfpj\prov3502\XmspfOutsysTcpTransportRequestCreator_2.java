package com.psbc.pfpj.prov3502;

import java.util.HashMap;
import java.util.Map;

import com.eos.system.utility.StringUtil;
import com.primeton.btp.api.core.exception.BTPRuntimeException;
import com.primeton.btp.api.core.logger.ILogger;
import com.primeton.btp.api.core.logger.LoggerFactory;
import com.primeton.btp.spi.transport.TxCodeRelativeInfo;
import com.primeton.btp.spi.transport.tcp.AbstractTcpTransportRequestCreator;
import com.primeton.components.api.message.MessagePackers;
import com.primeton.components.spi.message.IMessagePacker;
import com.primeton.components.spi.message.PackContext;
import com.primeton.components.spi.message.def.AbstractMessageDefinition;
import com.primeton.components.spi.message.def.MessageDefinitionSet;
import com.primeton.components.spi.message.def.model.FieldType;

public class XmspfOutsysTcpTransportRequestCreator_2 extends AbstractTcpTransportRequestCreator {
	private ILogger log=LoggerFactory.getLogger(XmspfOutsysTcpTransportRequestCreator_2.class);
	
	@Override
	public TxCodeRelativeInfo getActrualTxCodeInfo(Object data, Object arg1) 
	  throws BTPRuntimeException {
		long start = System.currentTimeMillis();
		String systemFieldRef = getTransport().getDefinition().getSystemFieldRef();
		AbstractMessageDefinition messageDefinition = MessageDefinitionSet.INSTANCE.getMessageDefinition(systemFieldRef);
		IMessagePacker messagePacker = MessagePackers.getMessagePacker(messageDefinition);
		String encoding = getTransport().getDefinition().getEncoding();
		PackContext context = new PackContext();
		context.setEncoding(encoding);
		context.setRequestId(String.valueOf(arg1));
		TxCodeRelativeInfo info = new TxCodeRelativeInfo();
		try {
			messagePacker.unpack((byte[])data, messageDefinition, context);
		} catch (Exception e) {
			log.error("["+context.getRequestId()+"] 预解包出错",e);
			throw new BTPRuntimeException("预解包出错",e);
		}
		log.error("===========================>"+context.getSysFields());
		log.error("===========================>"+context.size());
		log.error("===========================>"+String.valueOf(data));
		
		String txcode = String.valueOf(context.getSystemField(FieldType.TRANS_CODE.getName()));
		if(StringUtil.isNullOrBlank(txcode)){
			log.error("["+context.getRequestId()+"] 未找到交易码");
			throw new BTPRuntimeException("未找到交易码 PCODE");
		}
		log.error("["+context.getRequestId()+"] 请求交易码："+txcode);
		
		txcode="xmspf_"+txcode;
		
		info.setEasyAcquireTxCode(true);
		info.setTxCode(txcode);
		long end = System.currentTimeMillis();
		logger.info("["+context.getRequestId()+"] SetData 耗时"+(end-start)+"毫秒");
		return info;
	}
}
