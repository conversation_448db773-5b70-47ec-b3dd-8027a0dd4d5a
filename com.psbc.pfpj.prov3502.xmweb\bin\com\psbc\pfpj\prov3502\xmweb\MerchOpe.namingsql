<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">
<sqlMap namespace="com.psbc.pfpj.prov3502.xmweb.MerchOpe">
    <!-- 管理端发起的业务代码添加 -->
    <insert id="opeadd" parameterClass="java.util.HashMap">INSERT INTO tb_ope_cd(
            ope_cd,
            ope_nm,
            update_tm
		)
      	VALUES(
			#ope_cd#,
            #ope_nm#,
            now()
       	)</insert>
    <!-- 管理端发起的业务代码删除 -->
    <delete id="opedel" parameterClass="java.util.HashMap">DELETE FROM
			tb_ope_cd
		WHERE 
			ope_cd = #ope_cd#</delete>
    <!-- 管理端发起的业务代码修改-->
    <update id="opeupe" parameterClass="java.util.HashMap">UPDATE 
    		tb_ope_cd
    	SET
    		ope_nm = #ope_nm#,
    		update_tm = now()
		WHERE
			ope_cd = #ope_cd#</update>
    <!-- 管理端发起的业务代码查询-->
    <select id="opeqry" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		SELECT
			ope_cd,
            ope_nm,
            to_char(update_tm,'YYYYMMDD') as update_tm
		FROM
			tb_ope_cd
		<dynamic prepend="where">
            <isNotEmpty prepend="AND" property="ope_cd">ope_cd like '%'||#ope_cd#||'%'</isNotEmpty>
            <isNotEmpty prepend="AND" property="ope_nm">ope_nm like '%'||#ope_nm#||'%'</isNotEmpty>
        </dynamic>	
		order by ope_cd
	</select>
    <!-- 管理端发起的委托机构添加 -->
    <insert id="merchadd" parameterClass="java.util.HashMap">INSERT INTO tb_merch_bas_info(
            merch_id,
			merch_nm,
			merch_on_off_fg,
			merch_inst_id,
			last_modify_tlr_id,
			last_modify_inst_id,
			last_modify_dt
		)
      	VALUES(
			#merch_id#,
			#merch_nm#,
			#merch_on_off_fg#,
			#merch_inst_id#,
			#last_modify_tlr_id#,
			#last_modify_inst_id#,
			now()
       	)</insert>
    <!-- 管理端发起的委托机构删除 -->
    <delete id="merchdel" parameterClass="java.util.HashMap">DELETE FROM
			tb_merch_bas_info
		WHERE 
			merch_id = #merch_id#</delete>
    <!-- 管理端发起的委托机构修改-->
    <update id="merchupe" parameterClass="java.util.HashMap">UPDATE 
    		tb_merch_bas_info
    	SET
    		merch_nm = #merch_nm#,
    		merch_on_off_fg = #merch_on_off_fg#,
    		last_modify_tlr_id = #last_modify_tlr_id#,
    		last_modify_inst_id = #last_modify_inst_id#,
    		last_modify_dt = now()
		WHERE
			merch_id = #merch_id#</update>
    <!-- 管理端发起的委托机构查询-->
    <select id="merchqry" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		SELECT
			merch_id,
			merch_nm,
			merch_on_off_fg,
			merch_inst_id,
			last_modify_tlr_id,
			last_modify_inst_id,
			to_char(last_modify_dt,'YYYYMMDD') as last_modify_dt 
		FROM
			tb_merch_bas_info
		<dynamic prepend="where">
            <isNotEmpty prepend="AND" property="merch_id">merch_id like '%'||#merch_id#||'%'</isNotEmpty>
            <isNotEmpty prepend="AND" property="ope_nm">merch_nm like '%'||#merch_nm#||'%'</isNotEmpty>
        </dynamic>
		order by merch_id
	</select>
    <!-- 管理端发起的业务代码委托机构关联添加 -->
    <insert id="merchopeadd" parameterClass="java.util.HashMap">INSERT INTO tb_merch_ope(
            merch_id,
			ope_cd,
			prdt_nm,
			merch_ope_on_off_fg,
			open_dt,
			merch_ope_type,
			summ_cd,
			cpcb_summ_cd,
			last_modify_tlr_id,
			last_modify_inst_id,
			last_modify_dt
		)
      	VALUES(
			#merch_id#,
			#ope_cd#,
			#prdt_nm#,
			#merch_ope_on_off_fg#,
			#open_dt#,
			#merch_ope_type#,
			#summ_cd#,
			#cpcb_summ_cd#,
			#last_modify_tlr_id#,
			#last_modify_inst_id#,
			now()
       	)</insert>
    <!-- 管理端发起的业务代码委托机构关联删除 -->
    <delete id="merchopedel" parameterClass="java.util.HashMap">DELETE FROM
			tb_merch_ope
		WHERE 
			merch_id = #merch_id#
			and ope_cd = #ope_cd#</delete>
    <!-- 管理端发起的业务代码委托机构关联修改-->
    <update id="merchopeupd" parameterClass="java.util.HashMap">UPDATE 
    		tb_merch_ope
    	SET
			prdt_nm = #prdt_nm#,
			merch_ope_on_off_fg = #merch_ope_on_off_fg#,
			open_dt = #open_dt#,
			merch_ope_type = #merch_ope_type#,
			summ_cd = #summ_cd#,
			cpcb_summ_cd = #cpcb_summ_cd#,
			last_modify_tlr_id = #last_modify_tlr_id#,
			last_modify_inst_id = #last_modify_inst_id#,
			last_modify_dt = now()
		WHERE
			merch_id = #merch_id#,
			and ope_cd = #ope_cd#,</update>
    <!-- 管理端发起的业务代码委托机构关联查询-->
    <select id="merchopeqry" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		SELECT
			ope_cd,
			merch_id,
			prdt_nm,
			merch_ope_on_off_fg,
			open_dt,
			merch_ope_type,
			summ_cd,
			cpcb_summ_cd,
			last_modify_tlr_id,
			last_modify_inst_id,
			to_char(last_modify_dt,'YYYYMMDD') as last_modify_dt
		FROM
			tb_merch_ope
		<dynamic prepend="where">
            <isNotEmpty prepend="AND" property="merch_id">merch_id like '%'||#merch_id#||'%'</isNotEmpty>
            <isNotEmpty prepend="AND" property="ope_cd">ope_cd like '%'||#ope_cd#||'%'</isNotEmpty>
        </dynamic>
		order by ope_cd,merch_id
	</select>
    <!-- 管理端发起的下拉选择业务代码查询-->
    <select id="selopeqry" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			ope_cd, 
			ope_nm,
			ope_cd || ' - ' || ope_nm as nms
		from tb_ope_cd
		where length(ope_cd) = 6
		<isNotEmpty prepend="and" property="ope_cd">ope_cd like #ope_cd#||'%'</isNotEmpty>
		order by ope_cd
		limit 10
	</select>
    <!-- 管理端发起的下拉选择委托机构查询-->
    <select id="selmerchqry" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
			select 
				a.merch_id,
				a.ope_cd,
				a.prdt_nm,
				a.merch_id || ' - ' || b.merch_nm as nms
			from tb_merch_ope a,tb_merch_bas_info b
			where a.merch_ope_on_off_fg = '1'
			and b.merch_on_off_fg = '1'
			and a.merch_id = b.merch_id
			and a.ope_cd = #ope_cd#
			and length(a.merch_id) = 12
			<isNotEmpty prepend="and" property="merch_id">a.merch_id like #merch_id#||'%'</isNotEmpty>
			order by a.ope_cd,a.merch_id
			limit 10
	</select>
    <!-- 管理端发起的下拉选择委托机构查询(关联查询添加使用)-->
    <select id="selmerchqry1" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			merch_id, 
			merch_nm,
			merch_id || ' - ' || merch_nm as nms
		from tb_merch_bas_info
		where length(merch_id) = 12
		<isNotEmpty prepend="and" property="merch_id">merch_id like #merch_id#||'%'</isNotEmpty>
		order by merch_id
		limit 10
	</select>
    <!-- 管理端发起的子业务参数维护添加 -->
    <insert id="subadd" parameterClass="java.util.HashMap">INSERT INTO tb_merch_sub_ope(
            merch_id,
			ope_cd,
            sub_ope_id,
			sub_ope_nm,
			sp_merch_id,
			merch_inst_id,
			busi_merch_id,
			bill_id,
			sub_merch_on_off_fg,
			open_dt,
			white_list_fg,
			summ_cd,
			cpcb_summ_cd,
			pay_limit_num,
			min_pay_at,
			max_pay_at,
			due_term,
			delay_rate,
			flag01,
			last_modify_tlr_id,
			last_modify_inst_id,
			last_modify_dt
		)
      	VALUES(
			#merch_id#,
			#ope_cd#,
            #sub_ope_id#,
			#sub_ope_nm#,
			#sp_merch_id#,
			#merch_inst_id#,
			#busi_merch_id#,
			#bill_id#,
			#sub_merch_on_off_fg#,
			#open_dt#,
			#white_list_fg#,
			#summ_cd#,
			#cpcb_summ_cd#,
			#pay_limit_num#,
			#min_pay_at#,
			#max_pay_at#,
			#due_term#,
			#delay_rate#,
			#flag01#,
			#last_modify_tlr_id#,
			#last_modify_inst_id#,
			now()
       	)</insert>
    <!-- 管理端发起的委托机构删除 -->
    <delete id="subdel" parameterClass="java.util.HashMap">DELETE FROM
			tb_merch_sub_ope
		WHERE 
			merch_id = #merch_id#
			and ope_cd = #ope_cd#
            and sub_ope_id = #sub_ope_id#</delete>
    <!-- 管理端发起的委托机构修改-->
    <update id="subupe" parameterClass="java.util.HashMap">UPDATE 
    		tb_merch_sub_ope
    	SET
			sub_ope_nm = #sub_ope_nm#,
			sp_merch_id = #sp_merch_id#,
			merch_inst_id = #merch_inst_id#,
			busi_merch_id = #busi_merch_id#,
			bill_id = #bill_id#,
			sub_merch_on_off_fg = #sub_merch_on_off_fg#,
			open_dt = #open_dt#,
			white_list_fg = #white_list_fg#,
			summ_cd = #summ_cd#,
			cpcb_summ_cd = #cpcb_summ_cd#,
			pay_limit_num = #pay_limit_num#,
			min_pay_at = #min_pay_at#,
			max_pay_at = #max_pay_at#,
			due_term = #due_term#,
			delay_rate = #delay_rate#,
			flag01 = #flag01#,
			last_modify_tlr_id = #last_modify_tlr_id#,
			last_modify_inst_id = #last_modify_inst_id#,
			last_modify_dt = now()
		WHERE
			merch_id = #merch_id#
			and ope_cd = #ope_cd#
            and sub_ope_id = #sub_ope_id#</update>
    <!-- 管理端发起的委托机构查询-->
    <select id="subqry" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		SELECT
			merch_id,
			ope_cd,
			sub_ope_id,
			sub_ope_nm,
			sp_merch_id,
			merch_inst_id,
			busi_merch_id,
			bill_id,
			sub_merch_on_off_fg,
			open_dt,
			white_list_fg,
			summ_cd,
			cpcb_summ_cd,
			pay_limit_num,
			min_pay_at,
			max_pay_at,
			due_term,
			delay_rate,
			flag01
		FROM
			tb_merch_sub_ope
		WHERE
			merch_id = #merch_id#
			and ope_cd = #ope_cd#
			<isNotEmpty property="sub_ope_id">and sub_ope_id = #sub_ope_id#</isNotEmpty>
        <isNotEmpty property="sub_ope_nm">and sub_ope_nm like '%'||#sub_ope_nm#||'%'</isNotEmpty>
		order by ope_cd,merch_id,sub_ope_nm
	</select>
</sqlMap>