<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_common.XMWEB_COMMON_0008RES" xmlns:common_head_res="prov3502_common.common_head_res" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_common.XMWEB_COMMON_0008RES">
    <xs:import namespace="prov3502_common.common_head_res" schemaLocation="../prov3502_common/common_head_res_msg_btx.xsd"/>
    <xs:complexType name="list">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="merch_id" nillable="true" type="xs:string"/>
            <xs:element name="ope_cd" nillable="true" type="xs:string"/>
            <xs:element name="tran_dt" nillable="true" type="xs:string"/>
            <xs:element name="merch_name" nillable="true" type="xs:string"/>
            <xs:element name="proc_memo" nillable="true" type="xs:string"/>
            <xs:element name="in_file_name" nillable="true" type="xs:string"/>
            <xs:element name="out_file_name" nillable="true" type="xs:string"/>
            <xs:element name="batch_id" nillable="true" type="xs:string"/>
            <xs:element name="tol_num" nillable="true" type="xs:string"/>
            <xs:element name="tol_amt" nillable="true" type="xs:string"/>
            <xs:element name="act_num" nillable="true" type="xs:string"/>
            <xs:element name="act_amt" nillable="true" type="xs:string"/>
            <xs:element name="proc_flag" nillable="true" type="xs:string"/>
            <xs:element name="proc_reason" nillable="true" type="xs:string"/>
            <xs:element name="load_time" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="body">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="list" nillable="true" type="list"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="XMWEB_COMMON_0008RES">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="head" nillable="true" type="common_head_res:common_head_res"/>
            <xs:element name="body" nillable="true" type="body"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
