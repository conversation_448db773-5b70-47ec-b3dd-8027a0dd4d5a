<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="com.xmspf_ds_head" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="com.xmspf_ds_head">
    <xs:complexType name="xmspf_ds_head">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="委托单位代码" nillable="true" type="xs:string"/>
            <xs:element name="业务代码" nillable="true" type="xs:string"/>
            <xs:element name="银行编码" nillable="true" type="xs:string"/>
            <xs:element name="外系统交易码" nillable="true" type="xs:string"/>
            <xs:element name="交易方日期" nillable="true" type="xs:string"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
