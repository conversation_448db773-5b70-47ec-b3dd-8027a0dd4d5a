<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="PSBC-CD173" category="message" create-date="2021-09-06 10:48:03" version="7.0.0.0">
  <message-definition bitmap-format="Binary" bitmap-length="0" bitmap-type="unionpay" category="xml" filter-null="false" id="xmspf_msghead_res" name="xmspf_msghead_res" namespace="xmspf.xmspf_msghead_res" orderly="false" xml-pack-simple-node="false" xml-pack-statement="true" nodef-json="false">
    <group-message-item display-name="head" name="head" seqno="0" xml-prefix="">
      <group-message-item display-name="statecode" name="statecode" seqno="0" xml-prefix="">
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STATE" display-name="执行结果" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="执行结果" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <ext-property/>
      </group-message-item>
      <group-message-item display-name="msg" name="msg" seqno="1" xml-prefix="">
        <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="STR50" display-name="执行结果描述" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="执行结果描述" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
          <ext-property/>
          <description></description>
        </message-item>
        <ext-property/>
      </group-message-item>
      <ext-property/>
    </group-message-item>
  </message-definition>
</configuration>