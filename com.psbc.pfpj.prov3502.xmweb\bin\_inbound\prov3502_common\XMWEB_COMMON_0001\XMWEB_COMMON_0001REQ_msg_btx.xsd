<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<xs:schema xmlns="prov3502_common.XMWEB_COMMON_0001REQ" xmlns:msg_common_0001Q="prov3502_8583.msg_common_0001Q" xmlns:msg_head_q="prov3502_8583.msg_head_q" xmlns:sdo="commonj.sdo" xmlns:sdoJava="commonj.sdo/java" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="prov3502_common.XMWEB_COMMON_0001REQ">
    <xs:import namespace="prov3502_8583.msg_head_q" schemaLocation="../prov3502_8583/msg_head_q_msg_btx.xsd"/>
    <xs:import namespace="prov3502_8583.msg_common_0001Q" schemaLocation="../prov3502_8583/msg_common_0001Q_msg_btx.xsd"/>
    <xs:complexType name="XMWEB_COMMON_0001REQ">
        <xs:sequence>
            <xs:any minOccurs="0"/>
            <xs:element name="报文头" nillable="true" type="msg_head_q:msg_head_q"/>
            <xs:element name="报文体" nillable="true" type="msg_common_0001Q:msg_common_0001Q"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
