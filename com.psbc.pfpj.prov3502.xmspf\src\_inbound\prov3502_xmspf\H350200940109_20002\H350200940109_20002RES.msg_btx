<?xml version="1.0" encoding="UTF-8"?>
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://www.primeton.com/btp/cfg" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" author="Administrator" category="message" create-date="2021-12-02 14:27:03" version="7.0.0.0">
  <message-definition appear-type="none" bitmap-format="Binary" bitmap-length="0" bitmap-type="unionpay" category="delimiter" delimiter="|" filter-null="false" id="H350200940109_20002RES" name="响应报文" namespace="prov3502_xmspf.H350200940109_20002RES" orderly="false" xml-pack-simple-node="false" xml-pack-statement="true" nodef-json="false">
    <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="RESPCODE" display-name="响应码" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="RESPCODE" pad-char="0x20" seqno="0" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
      <ext-property/>
      <description></description>
    </message-item>
    <message-item align="right" bitmap-field-filled-mode="left" bitmap-field-length="0" bitmap-field-type="fixedLength" bitmap-lenfield-length="0" bitmap-lenfield-type="string" bitmap-position="0" dict-id="RET_EXPLAIN" display-name="响应信息" field-type="normal" is-recheck="false" is-required="false" is-secure="false" length="0" modify-value="0" name="RESP_INFO" pad-char="0x20" seqno="1" validate-rule="" value-mode="normal" xml-field-type="VALUE" vary-field-length="0" message-field-type="ASCII">
      <ext-property/>
      <description></description>
    </message-item>
  </message-definition>
</configuration>