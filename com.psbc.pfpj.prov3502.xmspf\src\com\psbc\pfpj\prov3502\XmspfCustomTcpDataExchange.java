package com.psbc.pfpj.prov3502;

import java.io.UnsupportedEncodingException;

import com.primeton.btp.api.core.exception.BTPRuntimeException;
import com.primeton.btp.api.core.logger.ILogger;
import com.primeton.btp.api.core.logger.LoggerFactory;
import com.primeton.btp.api.core.utils.ExtPropertyHelper;
import com.primeton.btp.api.tcp.netty.buffer.ChannelBuffer;
import com.primeton.btp.api.tcp.netty.buffer.ChannelBuffers;
import com.primeton.btp.api.tcp.netty.channel.Channel;
import com.primeton.btp.api.tcp.netty.channel.ChannelHandlerContext;
import com.primeton.btp.api.transport.tcp.ITcpTransport;
import com.primeton.btp.api.transport.tcp.exception.ExceptionCodes;
import com.primeton.btp.spi.transport.tcp.ITcpDataExchange;
import com.primeton.btp.spi.transport.tcp.AbstractTcpTransport;

public class XmspfCustomTcpDataExchange implements ITcpDataExchange {
	private ILogger log = LoggerFactory.getLogger(XmspfCustomTcpDataExchange.class);
	private ITcpTransport transport;
	private static int length = 0;
	protected int TIME_LEN = 1350;
	protected int headBytesLength = 6;

	protected boolean lengthContainsHeadBytes = true;

	public static final int MAX_HEAD_BYTES_LENGTH = 6;

	public static final int DEFAULT_HEAD_BYTES_LENGTH = 6;

	public void setTransport(ITcpTransport transport) {
		this.transport = transport;
		this.headBytesLength = ExtPropertyHelper.getEntryValueAsInt(transport.getDefinition().getExtProperty(),
				"HEAD_BYTES_LENGTH", 6);
		if (this.headBytesLength <= 0 || this.headBytesLength > 6)
			throw new BTPRuntimeException(ExceptionCodes.SPECIFIED_HEAD_BYTES_LENGTH_ERROR,new String[] { String.valueOf(this.headBytesLength), String.valueOf(6) });
	}

	public byte[] read(ChannelBuffer buffer) {
		return read(null, null, buffer, null);
	}
	
	public byte[] read(ChannelHandlerContext ctx, Channel channel, ChannelBuffer buffer,
			AbstractTcpTransport transport) {

		log.info("收到厦门商品房委托方发起请求");
		
		log.info(buffer.readableBytes()+":"+this.headBytesLength);

		int readableBytes = buffer.readableBytes();
//		byte[] data = new byte[readableBytes];
//		buffer.readBytes(data);
//		String orgRecvData = new String(data);
	
		log.error("接收到的报文长度:"+readableBytes);	
		//if (receiveLength <= 0 || receiveLength==1024) {
		if (readableBytes <= 0) {
			return null;
		}
		
//		byte[] len_body = new byte[4];	
//		buffer.getBytes(buffer.readerIndex(), len_body);
//		String lenStr = new String(len_body);
//		log.info("msg len:" + lenStr);
//		int msgLen = Integer.valueOf(lenStr);
//		
//		if (buffer.readableBytes() < msgLen) {
//			log.info("readableBytes < msgLen. return null.");
//			return null;
//		}
//		buffer.readBytes(len_body);
//		byte[] data = new byte[msgLen];
//		buffer.readBytes(data);
//		String orgRecvData = new String(data);
//		//log.info("接收:"+orgRecvData);
//		log.info("*****收到的报文内容：:" + orgRecvData);	
////		logger.info("接收:"+CustomUtils.byte2hex(data));
//		return data;
		
		log.info("length=" + length);
		if(length == 0){
			if(buffer.readableBytes() < 6){
				log.info("buffer readableBytes < 6");
				return null;
			}
			byte[] lengthBytes = new byte[6];
			buffer.readBytes(lengthBytes);
			length = Integer.parseInt(new String(lengthBytes));
			log.debug("报文长度[length] == "+length);
		}
		
		if(buffer.readableBytes() < length){
			log.debug("报文内容未读完");
			return null;
		}
	
		byte[] msgBytes = new byte[length];
		buffer.readBytes(msgBytes);
		//String rspStr = new String(msgBytes,"UTF-8");
		String rspStr = new String(msgBytes);
		log.debug("接收到的报文内容[receiveStr] == "+rspStr);
		length = 0;
		return rspStr.getBytes();
		
	
	}

	public ChannelBuffer write(byte[] bytes) {

		String orgSendData = new String(bytes);
		
		orgSendData = orgSendData.replaceAll("InstId1","InstId").replaceAll("InstId2","InstId");
		log.info("*****发送报文内容:" + orgSendData);
		
		int dataLength = 0;
		try {
			dataLength = orgSendData.getBytes("UTF-8").length;
		} catch (UnsupportedEncodingException e) {
			// TODO 自动生成的 catch 块
			e.printStackTrace();
		}
		String lenStr = String.format("%06d", dataLength);
		log.info("向发送给委托方的报文添加长度："+lenStr);
		
		String newSendData =lenStr+orgSendData;
		log.info("拼接后报文:"+newSendData);
		

		bytes=null;
		try {
			bytes= newSendData.getBytes("UTF-8");
		} catch (UnsupportedEncodingException e) {
			e.printStackTrace();
		}
		
		/*
		RsMessage rsm = new RsMessage();
		bytes=null;
		bytes= rsm.RsEncrypt(newSendData.getBytes());
		if(NoneType.isNull(bytes)){
			log.error("-------------------RsEncryptFail:报文加加密失败--------------------");
			return null;
		}*/
		ChannelBuffer sendBuffer = null;
		sendBuffer = ChannelBuffers.buffer(bytes.length);//length=15
		sendBuffer.writeBytes(bytes);
		return sendBuffer;
		
	}
    public static void main(String[] args) {
		String orgSendData="厦门商品房123";
		System.out.println("长度："+orgSendData.length());
		System.exit(0);
	}


    public ITcpTransport getTransport() {
		return this.transport;
	}
}
