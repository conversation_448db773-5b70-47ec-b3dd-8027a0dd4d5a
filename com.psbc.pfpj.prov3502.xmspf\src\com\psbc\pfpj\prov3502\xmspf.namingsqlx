<?xml version="1.0" encoding="UTF-8"?>
<!-- author:PSBC-CD173 -->
<sqlMap>
    <!-- 查询支付指令状态-->
	<select id="select_tb_zjjg_tran_info" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select tran_fg 
			from tb_zjjg_tran_info 
				where merch_id = #merch_id#
					and ope_cd =#ope_cd#
					and tran_dt is not null
					and tran_sq=#tran_sq#
					and trans_type=#trans_type#
	</select>
	<!-- 查询监管账户-->
	<select id="select_cpab_acc_id" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select * from tb_zjjg_acct_info 
		where merch_id = #merch_id# 
		  and ope_cd = #ope_cd#
		  and trans_type='zzg'
	</select>
	<!-- 查询监管账户-->
	<select id="check_acc" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select * from tb_zjjg_acct_info 
		where merch_id = #merch_id# 
		  and ope_cd = #ope_cd#
		  and trans_type='zzg'
		  and cpab_acc_id=#cpab_acc_id#
	</select>
	<!-- 新增监管账户信息记录 -->
    <insert id="add_tb_merch_ope_trans_info" parameterClass="java.util.HashMap">
    insert into tb_zjjg_tran_info(
		merch_id,
		ope_cd,
		trans_type,
		tran_dt,
		tran_sq,
		open_brh_id,
		open_brh_nm,
		cpab_acc_id,
		acct_nm,
		tran_at,
		vch_type,
		tran_fg,
		peer_brh_id,
		peer_brh_nm,
		peer_acc_id,
		peer_acc_nm,
		aptn_tx,
		tran_time,
		txn_brh_id,
		oth_msg4_tx,
		oth_msg5_tx)
	values(
		#merch_id#,
		#ope_cd#,
		#trans_type#,
		#tran_dt#,
		#tran_sq#,
		#open_brh_id#,
		#open_brh_nm#,
		#cpab_acc_id#,
		#acct_nm#,
		cast(#tran_at# as numeric),
		#vch_type#,
		#tran_fg#,
		#peer_brh_id#,
		#peer_brh_nm#,
		#peer_acc_id#,
		#peer_acc_nm#,
		#aptn_tx#,
		#tran_time#,
		#txn_brh_id#,
		#oth_msg4_tx#,
		#oth_msg5_tx#)
    </insert>
    
    <!-- 监管账户支付指令查询-->
	<select id="select_zjjg_tran_stat" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			t.tran_time,
			t.tran_sq,
			t.tran_fg,
			(case when t.oth_msg1_tx = '1' then '支付待复核' when t.oth_msg1_tx = '3' then '退款待复核' else (case when t.tran_fg='0' then '待支付' when t.tran_fg='1' then '已支付' else '退款' end) end) as tranfg,
			t.oth_msg1_tx,
			t.oth_msg2_tx,
			t.open_brh_id,
			t.open_brh_nm,
		    t.cpab_acc_id,
			t.acct_nm,
			(case when t.vch_type='1' then '支取' when t.vch_type='2' then '监管行互转' else '退款' end) as vch_type,
			t.tran_at,
			t.peer_brh_id,
			t.peer_brh_nm,
			t.peer_acc_id,
			t.peer_acc_nm,
			t.aptn_tx,
			t.actxt_tx,
			to_char(t.last_update_time, 'yyyymmddhh24miss') as last_update_time,
			t.txn_brh_id,
			i.inst_id as brh_id,
			i.inst_nm as brh_nm,
			t.tlr_id,
			t.checker_id
		from tb_zjjg_tran_info t
		left join tb_inst_info i on t.txn_brh_id = i.inst_id
		where t.merch_id = #merch_id#
		and t.ope_cd = #ope_cd#
		<isNotEmpty  property="bgn_date">
			and t.tran_dt <![CDATA[ >= #bgn_date# ]]>
		</isNotEmpty>
        <isNotEmpty  property="end_date">
			and t.tran_dt <![CDATA[ <= #end_date# ]]>
		</isNotEmpty>
		<isNotEmpty  property="tran_sq">
			and t.tran_sq = #tran_sq#
		</isNotEmpty>
		and t.TRANS_TYPE='zzg'
		and t.txn_brh_id =#orgcode#
		<isEqual property='busi_type' compareValue='0'>
			and t.tran_fg in ('0','1','3')
		</isEqual>
		<isEqual property='busi_type' compareValue='1'>
			and t.tran_fg not in ('0','1','3')
		</isEqual>
		<isEqual property='busi_state' compareValue='1'>
			and (t.oth_msg1_tx = null or t.oth_msg1_tx = '') and t.tran_fg = '0'
		</isEqual>
		<isEqual property='busi_state' compareValue='2'>
			and (t.oth_msg1_tx = null or t.oth_msg1_tx = '') and t.tran_fg = '1'
		</isEqual>
		<isEqual property='busi_state' compareValue='3'>
			and (t.oth_msg1_tx = null or t.oth_msg1_tx = '') and t.tran_fg = '2'
		</isEqual>
		<isEqual property='busi_state' compareValue='4'>
			and t.oth_msg1_tx = '1'
		</isEqual>
		<isEqual property='busi_state' compareValue='5'>
			and t.oth_msg1_tx = '3'
		</isEqual>
		order by
			t.tran_fg,
			last_update_time
	</select>
	
	<!-- 监管账户支付指令明细查询-->
	<select id="select_zjjg_tran_detail" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			t.tran_time,
			t.tran_sq,
			t.tran_fg,
			(case when t.tran_fg='0' then '待支付' when t.tran_fg='1' then '已支付' else '退款' end) as tranfg,
			t.oth_msg1_tx,
			t.oth_msg2_tx,
			t.open_brh_id,
			t.open_brh_nm,
			t.cpab_acc_id,
			t.acct_nm,
			t.vch_type,
			(case when t.vch_type='1' then '支取' when t.vch_type='2' then '监管行互转' else '退款' end) as vchtype,
			t.tran_at,
			t.peer_brh_id,
			t.peer_brh_nm,
			t.peer_acc_id,
			t.peer_acc_nm,
			t.aptn_tx,
			t.actxt_tx,
			to_char(t.last_update_time, 'yyyymmddhh24miss') as last_update_time,
			t.txn_brh_id,
			i.inst_id as brh_id,
			i.inst_nm as brh_nm,
			t.tlr_id,
			t.checker_id
		from tb_zjjg_tran_info t
		left join tb_inst_info i on t.txn_brh_id = i.inst_id
		where t.merch_id = #merch_id#
			and t.ope_cd = #ope_cd#
			and t.tran_dt is not null
			and t.tran_sq = #tran_sq#
			and t.trans_type = 'zzg'
			and (t.oth_msg1_tx is null or t.oth_msg1_tx = '')
		order by
			t.tran_fg,
			last_update_time
	</select>
	<!-- 监管账户待复核支付指令明细查询-->
	<select id="select_zjjg_tran_detail_review" parameterClass="java.util.HashMap" resultClass="java.util.HashMap">
		select 
			t.tran_time,
			t.tran_sq,
			t.tran_fg,
			(case when t.tran_fg='0' then '待支付' when t.tran_fg='1' then '已支付' else '退款' end) as tranfg,
			(case when t.oth_msg1_tx='0' then '待支付' when t.oth_msg1_tx='1' then '已支付' else '退款' end) as oth_msg1_tx,
			t.oth_msg2_tx,
			t.open_brh_id,
			t.open_brh_nm,
			t.cpab_acc_id,
			t.acct_nm,
			t.vch_type,
			(case when t.vch_type='1' then '支取' when t.vch_type='2' then '监管行互转' else '退款' end) as vchtype,
			t.tran_at,
			t.peer_brh_id,
			t.peer_brh_nm,
			t.peer_acc_id,
			t.peer_acc_nm,
			t.aptn_tx,
			t.actxt_tx,
			to_char(t.last_update_time, 'yyyymmddhh24miss') as last_update_time,
			t.txn_brh_id,
			i.inst_id as brh_id,
			i.inst_nm as brh_nm,
			t.tlr_id,
			t.checker_id
		from tb_zjjg_tran_info t
		left join tb_inst_info i on t.txn_brh_id = i.inst_id
		where t.merch_id = #merch_id#
			and t.ope_cd = #ope_cd#
			and t.tran_dt is not null
			<isNotEmpty property="tran_sq">
				and t.tran_sq = #tran_sq# 
			</isNotEmpty>
			and t.trans_type = 'zzg'
			<isEqual property='oth_msg1_tx' compareValue='1'>
				and t.oth_msg1_tx = '1'
			</isEqual>
			<isEqual property='oth_msg1_tx' compareValue='3'>
				and t.oth_msg1_tx = '3'
			</isEqual>
			<!-- and (t.oth_msg2_tx is null or t.oth_msg2_tx = '') -->
		order by
			t.tran_fg,
			last_update_time
	</select>
	<!-- 更新止付表金额 -->
    <update id="update_zjjg_tran_detail_review" parameterClass="java.util.HashMap">
    	update 
    		tb_zjjg_tran_info
    	set
    		tran_fg = #tran_fg#
    		<isNotEmpty property="actxt_tx">
				,actxt_tx=#actxt_tx#
			</isNotEmpty>
			<isNotEmpty property="last_update_time">
				,last_update_time=to_timestamp(#last_update_time#,'yyyymmddhh24miss')
			</isNotEmpty>
    	where
    		merch_id = #merch_id# and
			ope_cd = #ope_cd# and
			tran_sq = #tran_sq# and 
			trans_type='zzg'
    </update>
	<!-- 更新支付指令复核信息 -->
    <update id="update_zjjg_tran_detail" parameterClass="java.util.HashMap">
    	update 
    		tb_zjjg_tran_info
    	set
    		<isNotEmpty property="tran_fg">
    			tran_fg = #tran_fg#,			<!-- 原支付状态(0-收到指令，待支付，1-已支付，2-未支付，3-退款) -->
    		</isNotEmpty>
    		<isNotNull property="oth_msg1_tx">
    			oth_msg1_tx = #oth_msg1_tx#,	<!-- 待复核状态(0-收到指令，待支付，1-已支付，2-未支付，3-退款) -->
    		</isNotNull>
			<!-- <isNotNull property="oth_msg2_tx"> -->
				<!-- oth_msg2_tx = #oth_msg2_tx#,	  复核结果(1-通过 2-不通过) -->
			<!-- </isNotNull> -->
			<isNotEmpty property="tlr_id"> 
				tlr_id = #tlr_id#,				<!-- 操作员 -->
			</isNotEmpty>
			<isNotEmpty property="checker_id">
				checker_id = #checker_id#,		<!-- 复核员 -->
			</isNotEmpty>
			<isNotNull property="actxt_tx">
				actxt_tx=#actxt_tx#,			<!-- 清空退款原因 -->
			</isNotNull>
			<isNotNull property="last_update_time">
				last_update_time = to_timestamp(#last_update_time#,'yyyymmddhh24miss')
			</isNotNull>
			<isNull property="last_update_time">
				<isEqual property='clear_time' compareValue='1'>
					last_update_time = null
				</isEqual>
				<isEqual property='clear_time' compareValue='0'>
					last_update_time = last_update_time
				</isEqual>
			</isNull>
    	where
    		merch_id = #merch_id# and
			ope_cd = #ope_cd# and
			tran_sq = #tran_sq# and 
			trans_type='zzg'
    </update>
	
</sqlMap>