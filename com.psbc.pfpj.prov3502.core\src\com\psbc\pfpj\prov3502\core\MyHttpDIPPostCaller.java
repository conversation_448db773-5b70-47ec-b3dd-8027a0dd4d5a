/*******************************************************************************
 * $Header: /cvsroot/PTP50/code/btp/develop/src/server/com.primeton.btp.endpoint.http/src/main/java/com/primeton/btp/impl/endpoint/http/HttpEndpointPostCaller.java,v 1.2 2013/09/06 08:13:00 wuyh Exp $
 * $Revision: 1.2 $
 * $Date: 2013/09/06 08:13:00 $
 *
 *==============================================================================
 *
 * Copyright (c) 2001-2006 Primeton Technologies, Ltd.
 * All rights reserved.
 *
 * Created on 2011-2-21
 *******************************************************************************/


package com.psbc.pfpj.prov3502.core;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.primeton.btp.api.endpoint.IEndpointResponseCallback;
import com.primeton.btp.api.endpoint.http.IHttpEndpointDefinition;
import com.primeton.btp.api.engine.hosttrans.IHostTransServiceDefinition;
import com.primeton.btp.impl.endpoint.http.NUrlEncodedFormEntity;
import com.primeton.btp.impl.endpoint.log.EndpointSystemLogger;
import com.primeton.btp.spi.endpoint.http.AbstractHttpEndpoint;
import com.primeton.btp.spi.endpoint.http.AbstractHttpEndpointCaller;
import com.primeton.components.api.httpclient.apache.NameValuePair;
import com.primeton.components.api.httpclient.apache.client.methods.HttpPost;
import com.primeton.components.api.httpclient.apache.entity.AbstractHttpEntity;
import com.primeton.components.api.httpclient.apache.message.BasicNameValuePair;
import com.primeton.components.api.httpclient.apache.nio.client.HttpAsyncClient;
import com.primeton.components.api.httpclient.apache.nio.entity.NByteArrayEntity;
import com.primeton.components.api.httpclient.apache.nio.entity.NStringEntity;
import com.primeton.esb.message.ITipMessagePayload;
import com.primeton.tip.org.springframework.integration.core.Message;

/**
 * TODO 此处填写 class 信息
 *
 * <AUTHOR> (mailto:<EMAIL>)
 */
public class MyHttpDIPPostCaller extends AbstractHttpEndpointCaller {
	protected IHttpEndpointDefinition definition;
	protected boolean isBodyRequest;
	private EndpointSystemLogger systemLogger = null;

	/**
	 * @param endpoint
	 */
	public MyHttpDIPPostCaller(AbstractHttpEndpoint endpoint) {
		super(endpoint);
		this.definition = endpoint.getDefinition();
		isBodyRequest = definition.getRequestParamName() == null ? true : false;
	}



	/*
	 * (non-Javadoc)
	 *
	 * @see com.primeton.btp.spi.endpoint.http.AbstractHttpEndpointCaller#invoke(java.lang.String,
	 *      java.lang.String,
	 *      com.primeton.btp.api.engine.hosttrans.IHostTransServiceDefinition,
	 *      com.primeton.btp.api.endpoint.IEndpointResponseCallback)
	 */
	@Override
	protected void invoke(String requestId, Object sendData,
			IHostTransServiceDefinition serviceDefinition,
			IEndpointResponseCallback callback,Object message,String url) throws Throwable {
		systemLogger = new EndpointSystemLogger((Message) message,endpoint);
		HttpAsyncClient client = endpoint.getHttpClient();
		HttpPost httpPost = new HttpPost(url);
		AbstractHttpEntity entity = null;
		if (isBodyRequest) {
			if (sendData != null) {
				if(sendData instanceof byte[]) {
					entity = new NByteArrayEntity((byte[])sendData);
				}else{
					entity = new NStringEntity(sendData.toString(), definition.getEncoding());
				}
				entity.setContentType(definition.getRequestContentType());
			}
		} else {
			if (definition.getRequestParamName() != null) {
				List<NameValuePair> nvps = new ArrayList<NameValuePair>();
				if(sendData instanceof byte[]){
					sendData = new String((byte[])sendData,definition.getEncoding());
				}
				nvps.add(new BasicNameValuePair(definition.getRequestParamName(), sendData.toString()));
				entity = new NUrlEncodedFormEntity(nvps, definition.getEncoding());
			}
		}
		httpPost.setEntity(entity);
		
		/*增加头部扩展属性  add by xiepeng 20220118*/
		AddHeadMsg(httpPost,message);
		
		systemLogger.logOriginalMessage(requestId, sendData, true);
		client.execute(httpPost, new MyHttpRFDoubleCallback(url, this, requestId, endpoint, sendData, serviceDefinition, message));
	}
	
	private void AddHeadMsg(HttpPost httpPost, Object message) {
		ITipMessagePayload payload = (ITipMessagePayload) ((Message) message).getPayload();
		Map<String, Object> dataPool = payload.getSystemHeaders();
		
		// TODO 自动生成的方法存根
		String ext_head = definition.getExtProperty().getEntryValue("EXT_HEADMSG");
		if(ext_head != null){
			String[] str = ext_head.split("\\|");
			for(int i=0; i<str.length; i++){
				String[] str1 = str[i].split(":");
				if(str1[1].startsWith("$")){
					String dictName = str1[1].substring(1);
					String dictvalue = String.valueOf(dataPool.get(dictName));
					httpPost.addHeader(str1[0], dictvalue);
				}else{
					httpPost.addHeader(str1[0], str1[1]);
				}
			}
		}
	}
	
}

/*
 * 修改历史
 * $Log: HttpEndpointPostCaller.java,v $
 * Revision 1.2  2013/09/06 08:13:00  wuyh
 * update:代码编码转换为UTF-8
 *
 * Revision 1.1  2013/09/03 11:41:17  liuxiang
 * add:添加BTP的项目
 *
 * Revision 1.8  2011/04/18 00:53:00  wangwb
 * Update:增加空指针判断
 *
 * Revision 1.7  2011/03/31 10:08:56  wangwb
 * Update:修改性能测试中发现的问题
 *
 * Revision 1.6  2011/03/25 10:10:29  wangwb
 * Update:修改为不重试
 *
 * Revision 1.5  2011/03/24 03:31:53  wangwb
 * BUG:33062
 *
 * Revision 1.4  2011/03/21 03:12:05  wangwb
 * BUG:33033
 *
 * Revision 1.3  2011/03/02 01:52:32  wangwb
 * Update:根据返回的状态码抛出异常
 *
 * Revision 1.2  2011/02/23 12:50:23  wangwb
 * Update:增加对https的支持
 *
 * Revision 1.1  2011/02/22 06:20:47  wangwb
 * Update:增加http通道
 *
 */