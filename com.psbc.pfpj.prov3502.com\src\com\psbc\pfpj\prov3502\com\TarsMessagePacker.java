package com.psbc.pfpj.prov3502.com;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.StringWriter;
import java.io.UnsupportedEncodingException;
import java.io.Writer;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.eos.runtime.core.ApplicationContext;
import com.eos.system.utility.StringUtil;
import com.primeton.btp.api.core.exception.BTPRuntimeException;
import com.primeton.btp.api.core.logger.ILogger;
import com.primeton.btp.api.core.logger.LoggerFactory;
import com.primeton.btp.api.core.utils.ByteTools;
import com.primeton.btp.api.core.utils.StringTools;
import com.primeton.btp.api.data.DataDictSet;
import com.primeton.btp.api.data.IDataDict;
import com.primeton.btp.api.data.IDataType;
import com.primeton.btp.api.endpoint.EndpointManager;
import com.primeton.btp.api.endpoint.IEndpoint;
import com.primeton.btp.api.message.AbstractCustMessagePacker;
import com.primeton.btp.api.message.DataPoolUtil;
import com.primeton.btp.impl.core.trancode.variable.BussinessRuleLoader;
import com.primeton.btp.impl.core.trancode.variable.IBussinessRuleLoader;
import com.primeton.btp.impl.core.trancode.variable.TranCodeVariableModel;
import com.primeton.btp.impl.core.trancode.variable.TranCodeVariablesCache;
import com.primeton.btp.impl.data.type.BinaryDataType;
import com.primeton.btp.impl.data.type.MoneyDataType;
import com.primeton.btp.spi.core.si.MessageHeaderNames;
import com.primeton.components.spi.message.PackContext;
import com.primeton.components.spi.message.UnpackedResult;
import com.primeton.components.spi.message.def.AbstractMessageDefinition;
import com.primeton.data.sdo.impl.helper.DataFactoryImpl;
import com.primeton.tip.org.springframework.integration.core.Message;

import commonj.sdo.DataObject;
import commonj.sdo.helper.DataFactory;

@SuppressWarnings("all")
public class TarsMessagePacker extends
		AbstractCustMessagePacker {
	private static final ILogger logger = LoggerFactory
			.getLogger(TarsMessagePacker.class);

	// 读取'tx_code_mapping.properties'文件
	private static Properties properties = null;
	private static String C_TX_CODE = "TX_CODE";
	private static String C_MERCH_ID = "MERCH_ID";
	private static String C_OUTSYS_CODE = "OUTSYS_CODE";
	private static String CONSTANT = "constant";
	private static String MARK_FRIMCODE = "#";
	private static String MARK_SCRIPT = "AND$";
	private static String SPLIT = "$";
	private static String SPLITITEM = "\\$";
	private static String SPLIT_EQUAL = "=";
	private static String METHOD_NAME = "checkData";
	private AbstractMessageDefinition definition;

	@Override
	public Object pack(Object obj, AbstractMessageDefinition definition,
			PackContext context) throws RuntimeException {
		this.definition = definition;
		this.context = context;
		if (logger.isDebugEnabled()) {
			logger.debug("报文[" + definition.getNamespace() + "." + definition.getName() + "] 开始打包");
		}
		Object retObj = pack(obj);
		if (logger.isDebugEnabled()) {
			if (retObj == null) {
				logger.debug("报文打包结果为null");
			} else {
				logger.debug("报文打包结果\n"
						+ StringTools.toHexTable(retObj, encoding));
			}

		}
		return retObj;
	}

	@Override
	public Object pack(Object obj) {
		Message message = context.getMessage();
		Map<String, Object> dataPool = DataPoolUtil.getDataPool(message);
		//added by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
		//2020/03/12  begin
		JSONObject jsonForPack = new JSONObject();
		//added by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
		//2020/03/12 end
		/*deleted by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
				//2020/03/12  begin
		Writer out = new StringWriter();
		JSONWriter writer = new JSONWriter(out);
		//deleted by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
				//2020/03/12  end*/
		byte[] data = null;
		String[] filterItem = null;
		boolean itemFlag = false;
		if (context.isHostTrans()) {
			String endpointId = context.getEndpointId();
			IEndpoint iEndpoint = EndpointManager.INSTANCE.get(endpointId);
			String value = iEndpoint.getDefinition().getExtProperty()
					.getEntryValue("FILTER_ITEM");
			if (!StringUtil.isNullOrBlank(value)) {
				filterItem = value.split(SPLITITEM);
			}
		}

		try {
			//deleted by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
			//2020/03/12  begin
			//writer.object();
			//deleted by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
			//2020/03/12  end
			// kim start 20171016
			dataPool = txCodeMapping(dataPool);
			// kim end 20171016

			Iterator<String> iterator = dataPool.keySet().iterator();
			while (iterator.hasNext()) {
				itemFlag = false;
				String dictId = iterator.next();
				Object value = dataPool.get(dictId);
				if (filterItem != null) {
					for (String item : filterItem) {
						if (item.equals(dictId)) {
							itemFlag = true;
							break;
						}
					}
				}
				if (itemFlag) {
					continue;
				}

				if (value instanceof List) {
					logger.debug("[" + context.getRequestId() + "] 打包循环域");
					List list = (List) value;
					//added by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
					//2020/03/12  begin
					JSONArray array=new JSONArray();
					//array.add(dictId);2020/4/8  bug   Deleted by Xionggh 
					//added by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
					//2020/03/12  end
					//deleted by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
					//2020/03/12  begin
					//writer.key(dictId);
					//deleted by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
					//2020/03/12  end
					logger.debug("[" + context.getRequestId() + "] 打包数据字典：" + dictId);
					//deleted by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
					//2020/03/12  begin
					//writer.array();
					//deleted by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
					//2020/03/12  end
					for (Object item : list) {
						value = getFromatedValueOfDict(dictId, item);
						//modied&deleted by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
						//2020/03/12  begin
						//writer.value(item);
//						array.add(item);
						// 20230104 fix: 当循环域为null时，替换为""  -- yuzongfu
						if( null == item){
							array.add("");
						} else {
							array.add(item);
						}
						//modied&deleted by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
						//2020/03/12  end
						logger.debug("[" + context.getRequestId() + "] 打包数据字典值：" + dictId + " = " + item);
					}
					//modied&deleted by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
					//2020/03/12  begin
					//writer.endArray();
					jsonForPack.put(dictId, array);
					//modied&deleted by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
					//2020/03/12  end
				} else {
					if (!havingDataDict(dictId)) {
						continue;
					}
					value = getFromatedValueOfDict(dictId, value);
					//modified by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
					//2020/03/12  begin
					//writer.key(dictId);
					//writer.value(value);
					jsonForPack.put(dictId, value);
					//modified by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
					//2020/03/12  end
					logger.debug("[" + context.getRequestId() + "] 打包数据字典：" + dictId + " = " + value);
				}
			}
			//modi&deleted by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
			//2020/03/12  begin
				//writer.endObject();
				//data = out.toString().getBytes(context.getEncoding());
				data = jsonForPack.toString().getBytes(context.getEncoding());
			//modi&deleted by xiongguohong 因为org版json打包会对中文引号转义，改用阿里巴巴fastjson
			//2020/03/12  end
		} catch (Exception e) {
			logger.error("[ " + context.getRequestId() + " ] 打包数据池异常", e);
			throw new BTPRuntimeException("打包数据池异常", e);
		}
		return data;
	}

	private Object parseDataType(Object valueOfDict, String dataDict) {
		IDataDict dictDefinition = null;
		Object parsedValueOfDict = null;
		try {
			dictDefinition = DataDictSet.INSTANCE.getDataDict(dataDict);
		} catch (Exception e) {
			logger.warn("[" + context.getRequestId() + "] 未找到数据字典：" + dataDict);
		}
		if (null != dictDefinition) {
			IDataType dictDataType = dictDefinition.getDataType();
			if (dictDataType instanceof MoneyDataType) {
				String dataStr = StringTools.object2String(valueOfDict,
						context.getEncoding());
				valueOfDict = new BigDecimal(dataStr.trim());
				return valueOfDict;
			}
			parsedValueOfDict = dictDataType.parse(dictDefinition, valueOfDict,
					context);
			if (dictDataType instanceof BinaryDataType) {
				parsedValueOfDict = ByteTools
						.parseBCDToBytes((byte[]) parsedValueOfDict);
			}
		} else {
			parsedValueOfDict = String.valueOf(valueOfDict);
		}
		return parsedValueOfDict;
	}

	// 交易码转换 kim-20171016
	// change by gxt-20180126
	private Map<String, Object> txCodeMapping(Map<String, Object> dataPool) {
		logger.info("[" + context.getRequestId() + "]交易码转换：开始进行交易码转换。");
		String firmCode = (String) context.getMessage().getHeaders()
				.get(MessageHeaderNames.TARGET_SYSTEM);
		if (StringUtil.isNullOrBlank(firmCode)) {
			firmCode = (StringUtil.isNotNullAndBlank(((String) dataPool
					.get(C_OUTSYS_CODE)))) ? (String) dataPool
					.get(C_OUTSYS_CODE) : (String) dataPool.get(C_MERCH_ID);
		}
		if (StringUtil.isNullOrBlank(firmCode)) {
			logger.error("[" + context.getRequestId() + "]交易码转换：无法找到商户代码。");
			return dataPool;
		}

		List<TranCodeVariableModel> variableModelList = TranCodeVariablesCache.instance
				.getTranCodeMap(firmCode);
		if (variableModelList != null) {
			setTxcode(variableModelList, dataPool);
		}
		return dataPool;
	}

	@Override
	public Object unpack(byte[] data) {
		String jsonString = null;
		try {
			jsonString = new String(data, getEncoding());
		} catch (UnsupportedEncodingException e) {
			throw new BTPRuntimeException("Json String encoding Error.");
		}
		//DataObject dataObject = DataFactory.INSTANCE.create("aprept.bran_aprept_commREQ", "bran_aprept_commREQ");
		DataObject dataObject = DataFactoryImpl.eINSTANCE.create("commonj.sdo.DataObject");
		try {
			JSONObject jsonObj = JSON.parseObject(jsonString);
			Iterator<String> keys = jsonObj.keySet().iterator();
			while (keys.hasNext()) {
				String key = keys.next();
				Object value = jsonObj.get(key);
				if (value instanceof JSONArray) {
					JSONArray array = (JSONArray) value;
					List<Object> listValue = new ArrayList<Object>();
					logger.debug("[" + context.getRequestId() + "] 解包循环域");
					for (int i = 0; i < array.size(); i++) {
						Object opt = array.get(i);
						value = parseDataType(opt, key);
						if(value instanceof String){	
							value = ((String) value).replaceAll("\\\\\"", "\\\\\\\\\"");
							logger.debug("循环域转义后值为：" + value );
						}
						listValue.add(value);
						
						//作为接入打包类，必须返回object对象，add by zhuym 20211028
						dataObject.set(key, value);
						logger.debug("[" + context.getRequestId() + "] 解包数据字典：" + key + " = " + opt);
					}
					if (!context.containsSystemField(key)) {
						context.addListSystemField(key, listValue);
						
						//作为接入打包类，必须返回object对象，add by zhuym 20211028
						dataObject.set(key, listValue);
						logger.info("[" + context.getRequestId() + "] 数据池中未发现此循环域数据字典  " + key);
					} else {
						if (context.getFillType().equals(PackContext.FillType.COVER)) {
							// if (context.isRepeatItem()) {
							// 循环域也需要覆盖
							context.addListSystemField(key, listValue);
							logger.info("[" + context.getRequestId() + "] 主机服务的循环域解包，覆盖数据池。");
							
							//作为接入打包类，必须返回object对象，add by zhuym 20211028
							dataObject.set(key, listValue);
							// }
						}
					}
				} else {
					logger.debug("非循环域[" + key +  "]" + "转义前值为：" + value );
					//20240325 lq 新增对不存在数据字典内域的处理
					if (!havingDataDict(key)) {
						if(null == value){
							continue;
						}
						value = new String(ByteTools.getBytes(value, encoding),encoding);
						value = ((String) value).replaceAll("\\\\\"", "\\\\\\\\\"");
						if (!context.containsSystemField(key)) {
							dataObject.set(key, value);
							context.addSystemField(key, value);
							logger.debug("[" + context.getRequestId() + "] 解包数据字典（数据池中未包含）：" + key + " = " + value);
						}else{
							if (context.getFillType().equals(PackContext.FillType.COVER)) {
								dataObject.set(key, value);
								context.addSystemField(key, value);
								logger.debug("[" + context.getRequestId() + "] 解包数据字典（覆盖）：" + key + " = " + value);
							}
						}
					}else{
						if (!context.containsSystemField(key)) {
							value = parseDataType(value, key);
							if(value instanceof String){	
								value = ((String) value).replaceAll("\\\\\"", "\\\\\\\\\"");
							}
							context.addSystemField(key, value);
							
							//作为接入打包类，必须返回object对象，add by zhuym 20211028
							dataObject.set(key, value);
							logger.debug("[" + context.getRequestId() + "] 解包数据字典（数据池中未包含）：" + key + " = " + value);
						} else {
							if (context.getFillType().equals(PackContext.FillType.COVER)) {
								value = parseDataType(value, key);
								if(value instanceof String){	
									value = ((String) value).replaceAll("\\\\\"", "\\\\\\\\\"");
								}
								context.addSystemField(key, value);								
								//作为接入打包类，必须返回object对象，add by zhuym 20211028
								dataObject.set(key, value);
								logger.debug("[" + context.getRequestId() + "] 解包数据字典（覆盖）：" + key + " = " + value);
							}
						}
					}
				}
			}
		} catch (JSONException e) {
			logger.error("[ " + context.getRequestId() + " ] 解包异常", e);
			logger.error("[ " + context.getRequestId() + " ] 原始报文：" + StringTools.toHexTable(data, getEncoding()));
			throw new BTPRuntimeException("解包异常", e);
		} catch (UnsupportedEncodingException e) {
			logger.error("[ " + context.getRequestId() + " ] 解包异常", e);
			logger.error("[ " + context.getRequestId() + " ] 原始报文：" + StringTools.toHexTable(data, getEncoding()));
			throw new BTPRuntimeException("解包异常", e);
		}
		return dataObject;
	}

	private String getFromatedValueOfDict(String dataDict, Object valueOfDict) {
		IDataDict dictDefinition = null;
		try {
			dictDefinition = DataDictSet.INSTANCE.getDataDict(dataDict);
		} catch (Exception e) {
			logger.warn("[" + context.getRequestId() + "] 未找到数据字典：" + dataDict);
		}
		byte[] byteData;
		if (null != dictDefinition) {
			IDataType dictDataType = dictDefinition.getDataType();
			if (dictDataType instanceof MoneyDataType) {
				BigDecimal bd = (BigDecimal) valueOfDict;
				return bd.toPlainString();
			}
			Object formatedDataDict = dictDataType.format(dictDefinition,
					valueOfDict, context);
			byteData = ByteTools.getBytes(formatedDataDict, encoding);
			if (dictDataType instanceof BinaryDataType) {
				byteData = ByteTools.formatBytesToBCD(byteData);
			}
		} else {
			byteData = ByteTools.getBytes(valueOfDict, encoding);
		}
		if (null == byteData) {
			return "";
		}
		return StringTools.object2String(byteData, encoding);
	}

	private boolean havingDataDict(String dataDict) {
		try {
			IDataDict dictDefinition = DataDictSet.INSTANCE
					.getDataDict(dataDict);
			if (null == dictDefinition) {
				logger.warn("[" + context.getRequestId() + "] 未找到数据字典："
						+ dataDict);
				return false;
			}
			return true;
		} catch (Exception e) {
			logger.warn("[" + context.getRequestId() + "] 未找到数据字典：" + dataDict);
			return false;
		}
	}

	@Override
	public Object unpack(UnpackedResult unpackResult) {
		return null;
	}

	@Override
	public Object getCorrelationID(Object data,
			AbstractMessageDefinition definition) {
		return null;
	}

	// kim start 20171016 交易码转换
	public static Properties getProperties() {

		String propertiesPath = ApplicationContext.getInstance()
				.getApplicationConfigPath() + "/tx_code_mapping.properties";
		Properties properties = new Properties();
		FileInputStream inputStream = null;
		try {
			inputStream = new FileInputStream(propertiesPath);
			properties.load(inputStream);
			inputStream.close();
		} catch (Exception e) {
			logger.error("加载‘tx_code_mapping.properties’文件异常", e);
		}finally{
			if(null != inputStream){
				try {
					inputStream.close();
				} catch (IOException e) {
					logger.error("关闭流异常",e);
				}
			}
		}
		return properties;
	}

	// kim end 20171016 交易码转换
	TranCodeVariableModel variableModelTemp = null; 

	public void setTxcode(List<TranCodeVariableModel> transRuleList,
			Map<String, Object> dataPool) {
		TranCodeVariableModel variableModelforTxCode = null;
		IBussinessRuleLoader ruleLoader = null;
		boolean flag = false;
		for (int i = 0; i < transRuleList.size(); i++) {
			flag = false;
			variableModelTemp = transRuleList.get(i);
			if (variableModelTemp.getScript().equals(CONSTANT)) {
				logger.info("[" + context.getRequestId() + "]交易码转换：添加常量字段 为"+ variableModelTemp.getFml());
				logger.info("[" + context.getRequestId() + "]交易码转换：添加常量字段 值为"+ variableModelTemp.getValue());
				dataPool.put(variableModelTemp.getFml(),variableModelTemp.getValue());
				continue;
			}
			String clazz = variableModelTemp.getExtclass();
			ruleLoader = StringUtil.isNullOrBlank(clazz) ? new BussinessRuleLoader(): getDealClass(clazz);
			if (ruleLoader == null) {
				throw new BTPRuntimeException("交易码转换校验类为空");
			}
			if (ruleLoader.checkData(variableModelTemp.getScriptMap(), dataPool)&& !flag) {
				flag = true;
				variableModelforTxCode = variableModelTemp;
				if(StringUtil.isEmpty((String)dataPool.get(variableModelforTxCode.getFml()))){
				dataPool.put(variableModelforTxCode.getFml(),variableModelforTxCode.getValue());
				logger.info("[" + context.getRequestId() + "]交易码转换：交易规则为 " + variableModelforTxCode.getScript());
				logger.info("[" + context.getRequestId() + "]交易码转换：交易码转换后值为 " + variableModelforTxCode.getValue());
				}
				
			}

		}
	}

	private IBussinessRuleLoader getDealClass(String clazz) {
		// TODO 自动生成的方法存根

		Class classLoader;
		try {
			classLoader = Class.forName(clazz);
			try {
				return (IBussinessRuleLoader) classLoader.newInstance();
			} catch (InstantiationException e) {
				// TODO 自动生成的 catch 块
				e.printStackTrace();
			} catch (IllegalAccessException e) {
				// TODO 自动生成的 catch 块
				e.printStackTrace();
			}
		} catch (ClassNotFoundException e) {
			// TODO 自动生成的 catch 块
			e.printStackTrace();
		}
		return null;
	}
}
