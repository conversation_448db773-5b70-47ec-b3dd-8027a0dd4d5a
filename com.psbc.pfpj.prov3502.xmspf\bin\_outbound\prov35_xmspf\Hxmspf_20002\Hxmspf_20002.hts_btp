<?xml version="1.0" encoding="UTF-8"?>
<configuration author="Administrator" category="outbound" create-date="2021-11-05 15:57:03" version="*******" xsi:schemaLocation="http://www.primeton.com/btp/cfg common.xsd" xmlns="http://www.primeton.com/btp/cfg" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<outbound id="Hxmspf_20002">
	<name>Hxmspf_20002</name>
	<host-server-name></host-server-name>
	<description></description>
	<fill-type>COVER</fill-type>
	<fill-pool>true</fill-pool>
	<timeout>0</timeout>
	<timeout-action>IGNORE</timeout-action>
	<host-trans-code/>
	<host-cancel-service-id/>
	<endpoint-id>prov3502_xmspf</endpoint-id>
	<req-message-id>Hxmspf_20002REQ</req-message-id>
	<resp-message-id>Hxmspf_20002RES</resp-message-id>
	<exception-message-id>Hxmspf_20002EXCE</exception-message-id>
	<ext-property>
      <entry description="超时处理类，必须实现接口com.primeton.btp.api.engine.hosttrans.ICustomTimeoutAction" key="CUSTOM_TIMEOUT_ACTION_CLASSNAME" value=""/>
    </ext-property>
	<ext-class/>
	<is-one-way>false</is-one-way>
	<msg2file>false</msg2file>
</outbound>
</configuration>

